using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Sacrra.Membership.Business.DTOs.AdhocRplacementScheduleDTOs
{
    public class BureauLoadStatsOutputDTO
    {
        public int AdHocFileSubmissionId { get; set; }
        public int NumberOfRecordsReceived { get; set; }
        public int NumberOfRecordsMatched { get; set; }
        public int NumberOfRecordsMatchedButNotUpdated { get; set; }
        public int NumberOfRecordsMatchedAndUpdated { get; set; }
        public int NumberOfRecordsUnmatched { get; set; }
        public int? TotalNumberOfQE1RecordRemainingOnDBPostCleanup { get; set; }
        public DateTime? DateNewQE1ExtractSharedPostCleanup { get; set; }
        public int? NumberOfDuplicatesRemovedFromDBBasedOnExtract { get; set; }
        public int? NumberOfRecordsMigrated { get; set; }
        public int? NumberOfRecordsMergedAcrossSRNs { get; set; }
        public int? NumberOfRecordsMergedWithinSRN { get; set; }
        public int? NumberOfRecordsMatchedSuccessfullyConverted { get; set; }
    }
}
