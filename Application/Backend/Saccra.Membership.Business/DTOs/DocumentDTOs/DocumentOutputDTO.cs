using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Database;
using System;

namespace Sacrra.Membership.Business.DTOs
{
    public class DocumentOutputDTO : DocumentShared
    {
        public int Id { get; set; }
        public IdValuePairResource Category { get; set; }
        public IdValuePairResource Status { get; set; }
        public string LastUpdatedAt { get; set; }
        public bool IsRead { get; set; }
    }
}
