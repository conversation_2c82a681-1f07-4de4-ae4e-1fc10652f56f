using Newtonsoft.Json;
using Sacrra.Membership.Database;
using System.Collections.Generic;

namespace Sacrra.Membership.Business.DTOs
{
    public class MailStorageDTO : MailStorageShared { }

    public class MailDeliveryStatusDTO : MailDeliveryStatusShared { }

    public class MailUserVariablesDTO { }

    public class MailFlagsDTO : MailFlagShared { }

    public class MailEnvelopeDTO : MailEnvelopShared { }

    public class MailHeadersDTO : MailHeaderShared { }

    public class MailMessageDTO : MailMessageShared
    {
        public MailHeadersDTO Headers { get; set; }
        public List<MailAttachmentDTO> Attachments { get; set; }
    }

    public class MailItemDTO : MailItemShared
    {
        public List<object> Tags { get; set; }
        public MailStorageDTO Storage { get; set; }

        [JsonProperty("delivery-status")]
        public MailDeliveryStatusDTO DeliveryStatus { get; set; }
        public List<object> Campaigns { get; set; }

        [JsonProperty("user-variables")]
        public MailUserVariablesDTO UserVariables { get; set; }
        public MailFlagsDTO Flags { get; set; }

        public MailEnvelopeDTO Envelope { get; set; }
        public MailMessageDTO Message { get; set; }
    }
    public class MailPagingDTO : MailPagingShared { }
    public class MailAttachmentDTO : MailAttachmentShared { }
    public class MailEventDTO
    {
        public List<MailItemDTO> Items { get; set; }
        public MailPagingDTO Paging { get; set; }
    }
}
