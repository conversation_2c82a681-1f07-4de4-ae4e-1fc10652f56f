using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Sacrra.Membership.Business.DTOs
{
    public class MigrationPlanGenerationDTO
    {
        [JsonPropertyName("sourceProcessDefinitionId")]
        public string SourceProcessDefinitionId { get; set; }

        [JsonPropertyName("targetProcessDefinitionId")]
        public string TargetProcessDefinitionId { get; set; }

        [JsonPropertyName("instructions")]
        public List<MigrationPlanGenerationInstructionDTO> Instructions { get; set; }

        [JsonPropertyName("variables")]
        public object Variables { get; set; }
    }
}
