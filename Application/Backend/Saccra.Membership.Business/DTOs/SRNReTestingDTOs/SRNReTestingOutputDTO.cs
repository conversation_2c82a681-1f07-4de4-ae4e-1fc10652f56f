using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Sacrra.Membership.Business.DTOs.SRNReTestingDTOs
{
    public class SRNReTestingOutputDTO
    {
        public int SRNStatusUpdateHistoryId { get; set; }
        public string SRNFileTestingStatusReason { get; set; }
        public string SRNNumber { get; set; }
        public string FileType { get; set; }
        public bool IsDailyFile { get; set; }
        public bool IsMonthlyFile { get; set; }
        public string CurrentStatus { get; set; }
        public string UpdateType { get; set; }
        public string LastSubmission { get; set; }

        #region Daily File Dates
        public string DailyFileDevelopmentStartDate { get; set; }
        public string DailyFileDevelopmentEndDate { get; set; }
        public string DailyFileTestStartDate { get; set; }
        public string DailyFileTestEndDate { get; set; }
        public string DailyFileGoLiveDate { get; set; }
        #endregion

        #region Monthly File Dates
        public string MonthlyFileDevelopmentStartDate { get; set; }
        public string MonthlyFileDevelopmentEndDate { get; set; }
        public string MonthlyFileTestStartDate { get; set; }
        public string MonthlyFileTestEndDate { get; set; }
        public string MonthlyFileGoLiveDate { get; set; }
        #endregion
    }
}
