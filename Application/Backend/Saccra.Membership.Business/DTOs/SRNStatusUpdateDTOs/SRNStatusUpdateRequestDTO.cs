using Sacrra.Membership.Database.Enums;
using System;
using System.ComponentModel.DataAnnotations;

namespace Sacrra.Membership.Business.DTOs
{
    public class SRNStatusUpdateRequestDTO
    {
        #region Daily File Dates
        [Display(Name = "Daily File Development Start Date")]
        public DateTime? DailyFileDevelopmentStartDate { get; set; }

        [Display(Name = "Daily File Development End Date")]
        public DateTime? DailyFileDevelopmentEndDate { get; set; }

        [Display(Name = "Daily File Test Start Date")]
        public DateTime? DailyFileTestStartDate { get; set; }

        [Display(Name = "Daily File Test End Date")]
        public DateTime? DailyFileTestEndDate { get; set; }

        [Display(Name = "Daily File Go Live Date")]
        public DateTime? DailyFileGoLiveDate { get; set; }

        #endregion

        #region Monthly File Dates

        [Display(Name = "Monthly File Development Start Date")]
        public DateTime? MonthlyFileDevelopmentStartDate { get; set; }

        [Display(Name = "Monthly File Development End Date")]
        public DateTime? MonthlyFileDevelopmentEndDate { get; set; }

        [Display(Name = "Monthly File Test Start Date")]
        public DateTime? MonthlyFileTestStartDate { get; set; }

        [Display(Name = "Monthly File Test End Date")]
        public DateTime? MonthlyFileTestEndDate { get; set; }

        [Display(Name = "Monthly File Go Live Date")]
        public DateTime? MonthlyFileGoLiveDate { get; set; }

        #endregion

        public bool IsLiveFileSubmissionsSuspended { get; set; }
        public string Comments { get; set; }
        public SRNStatusTypes? UpdateTypeId { get; set; }
        public string BureauInstruction { get; set; }
        public SRNStatusFileTypes? FileTypeId { get; set; }
        public DateTime? LastSubmissionDate { get; set; }
        public int Id { get; set; }
        public DateTime? AccountStatusDate { get; set; }
        public int SRNStatusId { get; set; }
        public int? SRNStatusReasonId { get; set; }
    }
}
