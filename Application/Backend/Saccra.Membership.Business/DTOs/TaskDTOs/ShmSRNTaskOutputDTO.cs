using Sacrra.Membership.Business.Resources.IdValuePair;

namespace Sacrra.Membership.Business.DTOs
{
    public class ShmSRNTaskOutputDTO
    {
        public string Id { get; set; }
        public string TaskDefinitionKey { get; set; }
        public int SRNId { get; set; }
        public IdValuePairResource FileType { get; set; }
        public string FileTypeValue { get; set; }
        public string TaskDate { get; set; }
        public string TaskType { get; set; }
        public string SRNNumber { get; set; }
        public string SRNDisplayName { get; set; }
        public string Member { get; set; }
        public string ALGLeader { get; set; }
    }
}
