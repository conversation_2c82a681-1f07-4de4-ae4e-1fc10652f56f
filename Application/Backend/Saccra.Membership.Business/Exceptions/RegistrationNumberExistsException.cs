using Sacrra.Membership.Business.Exceptions;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Sacrra.Membership.Business.Exceptions
{
    public class RegistrationNumberExistsException: CustomApiException {
        public RegistrationNumberExistsException() : base() { }

        public RegistrationNumberExistsException(int statusCode, string message, string stackTrace) : base(statusCode, message, stackTrace) { }
    }
}
