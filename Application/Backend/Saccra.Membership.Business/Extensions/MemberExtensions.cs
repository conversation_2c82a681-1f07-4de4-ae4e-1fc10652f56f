using AutoMapper;
using Camunda.Api.Client;
using Camunda.Api.Client.ProcessDefinition;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Business.Resources.Member;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;
using Sacrra.Membership.Business.Resources.MemberContact;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace Sacrra.Membership.Business.Extensions
{
    public class MemberExtensions
    {
        public async Task ApplyMemberChanges(AppDbContext _dbContext, Member member, IMapper _mapper, MemberUpdateAllTypesResource modelForUpdate)
        {
            var user = await Helpers.Helpers.GetUserByAuth0Id(_dbContext);

            if (member != null)
            {
                MemberStagingChangeLogResource stagingChangeLog = new MemberStagingChangeLogResource();

                UpdateMemberContacts(_dbContext, member, modelForUpdate, stagingChangeLog);
                UpdateMemberTradingNames(member, modelForUpdate, stagingChangeLog);
                await UpdateALGLeaders(_dbContext, member.Id, modelForUpdate, stagingChangeLog, member);

                var model = _mapper.Map(modelForUpdate, member);
                model.Id = member.Id;

                var idDocumentJson = JsonConvert.SerializeObject(modelForUpdate.IDDocument);
                var ncrCertificateJson = JsonConvert.SerializeObject(modelForUpdate.NcrCertificate);

                if (modelForUpdate.PrincipleDebtRangeId > 0)
                    model.NcrCategory = ((PrincipleDebtRanges)Enum.Parse(typeof(PrincipleDebtRanges), "N" + modelForUpdate.PrincipleDebtRangeId)).ToString();

                model.ApplicationStatusId = member.ApplicationStatusId;
                model.StakeholderManagerId = member.StakeholderManagerId;

                if (model.PrimaryBureauId <= 0)
                    model.PrimaryBureauId = null;
                if (model.SecondaryBureauId <= 0)
                    model.SecondaryBureauId = null;

                model.IdNumber = (!string.IsNullOrEmpty(model.IdNumber)) ? model.IdNumber : null;
                model.RegisteredNumber = (!string.IsNullOrEmpty(model.RegisteredNumber)) ? model.RegisteredNumber : null;

                _dbContext.Set<Member>().Update(model);

                var memberDoc = await _dbContext.MemberDocuments
                    .AsNoTracking()
                    .FirstOrDefaultAsync(i => i.MemberId == member.Id);

                if (memberDoc == null)
                    memberDoc = new MemberDocument();

                CreateMemberDocumentsChangeLog(memberDoc, idDocumentJson, ncrCertificateJson, stagingChangeLog);

                memberDoc.MemberId = member.Id;
                memberDoc.IDDocumentBlob = idDocumentJson;
                memberDoc.NcrCertificateBlob = ncrCertificateJson;
                _dbContext.Set<MemberDocument>().Update(memberDoc);

                var memberDetails = await _dbContext.ALGMemberDetails
                    .AsNoTracking()
                    .FirstOrDefaultAsync(i => i.MemberId == member.Id);

                if (model.MembershipTypeId == MembershipTypes.ALGLeader)
                {
                    CreateALGMemberDetailsChangeLog(memberDetails, modelForUpdate.NumberOfClients, modelForUpdate.LoanManagementSystemName, stagingChangeLog);
                }

                if (memberDetails != null)
                {
                    memberDetails.NumberOfClients = modelForUpdate.NumberOfClients;
                    memberDetails.LoanManagementSystemName = modelForUpdate.LoanManagementSystemName;
                    _dbContext.Update(memberDetails);
                }
                else if (memberDetails == null)
                {
                    if (member.MembershipTypeId == MembershipTypes.ALGLeader)
                    {
                        memberDetails = new ALGMemberDetails
                        {
                            MemberId = member.Id,
                            NumberOfClients = modelForUpdate.NumberOfClients,
                            LoanManagementSystemName = modelForUpdate.LoanManagementSystemName
                        };
                        _dbContext.Add(memberDetails);
                    }
                }

                await _dbContext.SaveChangesAsync();

                await CreateMemberChangeLog(_dbContext, modelForUpdate, member, stagingChangeLog);

                var updateDetailsBlob = JsonConvert.SerializeObject(modelForUpdate);
                var stagingDetailsBlob = JsonConvert.SerializeObject(stagingChangeLog);

                if (stagingChangeLog.Changes.Count > 0)
                {
                    await Helpers.Helpers
                        .CreateEventLog(_dbContext, user.Id, "Member Update", member.RegisteredName, updateDetailsBlob, stagingDetailsBlob, member.Id, "Member");
                }
            }
        }
        private void UpdateMemberContacts(AppDbContext _dbContext, Member member, MemberUpdateResource modelForUpdate, MemberStagingChangeLogResource stagingChangeLog)
        {
            //Add new trading names and update existing ones
            var lstExistingContacts = new List<MemberContact>();
            if (modelForUpdate != null)
            {
                if (modelForUpdate.Contacts != null)
                {
                    var newContacts = new List<MemberContactUpdateResource>();
                    foreach (var contact in modelForUpdate.Contacts)
                    {
                        if (!member.Contacts.Any(i => i.Id == contact.Id))
                        {
                            newContacts.Add(contact);
                        }

                        else
                        {
                            var existingContact = member.Contacts
                                .FirstOrDefault(i => i.Id == contact.Id);

                            if (existingContact.FirstName != contact.FirstName)
                            {
                                var stagingChange = new StagingChange
                                {
                                    Name = "Contact First Name",
                                    OldValue = existingContact.FirstName,
                                    NewValue = contact.FirstName
                                };

                                stagingChangeLog.Changes.Add(stagingChange);
                            }
                            existingContact.FirstName = contact.FirstName;

                            if (existingContact.Surname != contact.LastName)
                            {
                                var stagingChange = new StagingChange
                                {
                                    Name = "Contact Surname",
                                    OldValue = existingContact.Surname,
                                    NewValue = contact.LastName
                                };

                                stagingChangeLog.Changes.Add(stagingChange);
                            }

                            existingContact.Surname = contact.LastName;

                            if (existingContact.CellNumber != contact.CellphoneNumber)
                            {
                                var stagingChange = new StagingChange
                                {
                                    Name = "Contact Cell Number",
                                    OldValue = existingContact.CellNumber,
                                    NewValue = contact.CellphoneNumber
                                };

                                stagingChangeLog.Changes.Add(stagingChange);
                            }

                            existingContact.CellNumber = contact.CellphoneNumber;

                            if (existingContact.Email != contact.EmailAddress)
                            {
                                var stagingChange = new StagingChange
                                {
                                    Name = "Contact Email",
                                    OldValue = existingContact.Email,
                                    NewValue = contact.EmailAddress
                                };

                                stagingChangeLog.Changes.Add(stagingChange);
                            }

                            existingContact.Email = contact.EmailAddress;

                            if (existingContact.OfficeTelNumber != contact.OfficeNumber)
                            {
                                var stagingChange = new StagingChange
                                {
                                    Name = "Contact Office Tel Number",
                                    OldValue = existingContact.OfficeTelNumber,
                                    NewValue = contact.OfficeNumber
                                };

                                stagingChangeLog.Changes.Add(stagingChange);
                            }

                            existingContact.OfficeTelNumber = contact.OfficeNumber;

                            if (existingContact.ContactTypeId != contact.ContactTypeId)
                            {
                                var oldType = _dbContext.ContactTypes.FirstOrDefault(i => i.Id == existingContact.ContactTypeId);
                                var newType = _dbContext.ContactTypes.FirstOrDefault(i => i.Id == contact.ContactTypeId);

                                var stagingChange = new StagingChange
                                {
                                    Name = "Contact Contact Type",
                                    OldValue = oldType.Name,
                                    NewValue = newType.Name
                                };

                                stagingChangeLog.Changes.Add(stagingChange);
                            }

                            existingContact.ContactTypeId = contact.ContactTypeId;

                            if (existingContact.JobTitle != contact.Designation)
                            {
                                var stagingChange = new StagingChange
                                {
                                    Name = "Contact Job Title",
                                    OldValue = existingContact.JobTitle,
                                    NewValue = contact.Designation
                                };

                                stagingChangeLog.Changes.Add(stagingChange);
                            }

                            existingContact.JobTitle = contact.Designation;

                            lstExistingContacts.Add(existingContact);
                        }
                    }
                }
            }

            //Delete all contacts that were removed from the update recource
            var deletedContacts = new List<MemberContact>();

            foreach (var contact in member.Contacts)
            {
                var tradingNameFound = modelForUpdate.Contacts.FirstOrDefault(i => i.Id == contact.Id);
                if (tradingNameFound == null)
                    deletedContacts.Add(contact);
            }

            if (deletedContacts.Count > 0)
            {
                foreach (var contact in deletedContacts)
                {
                    member.Contacts.Remove(contact);
                    _dbContext.Entry(contact).State = EntityState.Deleted;
                }
            }
        }

        private void UpdateMemberTradingNames(Member member, MemberUpdateResource modelForUpdate, MemberStagingChangeLogResource stagingChangeLog)
        {
            //Add new trading names and update existing ones
            var lstExistingTradingNames = new List<TradingName>();
            if (modelForUpdate != null)
            {
                if (modelForUpdate.TradingNames != null)
                {
                    var newTradingNames = new List<TradingNameUpdateResource>();
                    foreach (var tradingName in modelForUpdate.TradingNames)
                    {
                        if (!member.TradingNames.Any(i => i.Id == tradingName.Id))
                        {
                            newTradingNames.Add(tradingName);

                            var stagingChange = new StagingChange
                            {
                                Name = "Member Trading Name",
                                OldValue = "",
                                NewValue = tradingName.Name
                            };

                            stagingChangeLog.Changes.Add(stagingChange);
                        }

                        else
                        {
                            var existingTradingName = member.TradingNames
                                .FirstOrDefault(i => i.Id == tradingName.Id);

                            if (existingTradingName.Name != tradingName.Name)
                            {
                                var stagingChange = new StagingChange
                                {
                                    Name = "Member Trading Name",
                                    OldValue = existingTradingName.Name,
                                    NewValue = tradingName.Name
                                };

                                stagingChangeLog.Changes.Add(stagingChange);
                            }

                            existingTradingName.Name = tradingName.Name;

                            lstExistingTradingNames.Add(existingTradingName);
                        }
                    }
                    modelForUpdate.TradingNames = newTradingNames;
                }
            }

            //Delete all trading names that were removed from the update recource
            var deletedTradingNames = new List<TradingName>();

            if(member.TradingNames != null)
            {
                foreach (var tradingName in member.TradingNames)
                {
                    var tradingNameFound = lstExistingTradingNames.FirstOrDefault(i => i.Id == tradingName.Id);
                    if (tradingNameFound == null)
                        deletedTradingNames.Add(tradingName);
                }
            }
            
            if (deletedTradingNames.Count > 0)
            {
                foreach (var tradingName in deletedTradingNames)
                {
                    member.TradingNames.Remove(tradingName);
                }
            }
        }
        private async Task<MemberStagingChangeLogResource> CreateMemberChangeLog(AppDbContext _dbContext, MemberUpdateAllTypesResource modelForUpdate, Member member, MemberStagingChangeLogResource memberStagingChangeLog)
        {
            if (modelForUpdate != null)
            {
                var updateModelType = modelForUpdate.GetType();
                var memberModelType = member.GetType();
                IList<PropertyInfo> updateProperties = new List<PropertyInfo>(updateModelType.GetProperties());
                IList<PropertyInfo> memberProperties = new List<PropertyInfo>(memberModelType.GetProperties());
                List<StagingChange> stagingChangeList = new List<StagingChange>();

                foreach (PropertyInfo updateProp in updateProperties)
                {
                    object updatePropValue = updateProp.GetValue(modelForUpdate, null);

                    var memberProp = memberProperties.FirstOrDefault(i => i.Name == updateProp.Name);
                    if (memberProp != null)
                    {
                        //Primary keys don't get updated
                        if (memberProp.Name != "MemberId")
                        {
                            object memberPropValue = memberProp.GetValue(member, null);

                            if (memberPropValue != null)
                            {
                                var propType = memberPropValue.GetType();
                                if (propType.IsPrimitive || propType == (typeof(System.String)))
                                {
                                    string oldValue = "";
                                    string newValue = "";

                                    if (updatePropValue == null)
                                        newValue = "";
                                    else
                                        newValue = updatePropValue.ToString();
                                    if (memberPropValue == null)
                                        oldValue = "";
                                    else
                                        oldValue = memberPropValue.ToString();

                                    if (newValue != oldValue)
                                    {
                                        //Foreign Keys
                                        if (memberProp.Name == "PrimaryBureauId" || memberProp.Name == "SecondaryBureauId")
                                        {
                                            if (!string.IsNullOrEmpty(oldValue))
                                            {
                                                var oldBureau = await _dbContext.Members
                                                .AsNoTracking()
                                                .FirstOrDefaultAsync(i => i.Id == (int)memberPropValue);

                                                if (oldBureau != null)
                                                    oldValue = oldBureau.RegisteredName;
                                            }

                                            if (!string.IsNullOrEmpty(newValue))
                                            {
                                                var newBureau = await _dbContext.Members
                                                .AsNoTracking()
                                                .FirstOrDefaultAsync(i => i.Id == (int)updatePropValue);

                                                if (newBureau != null)
                                                    newValue = newBureau.RegisteredName;
                                            }
                                        }
                                        else
                                        {
                                            if (!string.IsNullOrEmpty(oldValue))
                                                oldValue = memberPropValue.ToString();
                                            if (!string.IsNullOrEmpty(newValue))
                                                newValue = updatePropValue.ToString();
                                        }

                                        var stagingChange = new StagingChange
                                        {
                                            Name = Helpers.Helpers.GetPropertyDisplayName(updateProp),
                                            OldValue = oldValue,
                                            NewValue = newValue
                                        };

                                        stagingChangeList.Add(stagingChange);
                                    }
                                }
                                else if (propType.IsEnum)
                                {
                                    string oldValue = "";
                                    string newValue = "";

                                    if (updatePropValue == null)
                                        newValue = "";
                                    else
                                        newValue = updatePropValue.ToString();
                                    if (memberPropValue == null)
                                        oldValue = "";
                                    else
                                        oldValue = memberPropValue.ToString();

                                    if (newValue != oldValue)
                                    {
                                        if (!string.IsNullOrEmpty(newValue))
                                            newValue = Helpers.Helpers.GetEnumValue(memberProp.Name, (int)updatePropValue);
                                        if (!string.IsNullOrEmpty(oldValue))
                                            oldValue = Helpers.Helpers.GetEnumValue(memberProp.Name, (int)memberPropValue);

                                        var stagingChange = new StagingChange
                                        {
                                            Name = Helpers.Helpers.GetPropertyDisplayName(updateProp),
                                            OldValue = oldValue,
                                            NewValue = newValue
                                        };

                                        stagingChangeList.Add(stagingChange);
                                    }
                                }
                            }
                        }
                    }
                }

                memberStagingChangeLog.Changes.AddRange(stagingChangeList);
                return memberStagingChangeLog;
            }

            return null;
        }
        private void CreateMemberDocumentsChangeLog(MemberDocument memberDocument, string idDocumentJson, string ncrCertificateJson, MemberStagingChangeLogResource stagingChangeLog)
        {
            if (memberDocument != null)
            {
                List<StagingChange> stagingChangeList = new List<StagingChange>();

                if (memberDocument.IDDocumentBlob != idDocumentJson)
                {
                    var stagingChange = new StagingChange
                    {
                        Name = "ID Document",
                        OldValue = memberDocument.IDDocumentBlob,
                        NewValue = idDocumentJson
                    };

                    stagingChangeList.Add(stagingChange);
                }
                if (memberDocument.NcrCertificateBlob != ncrCertificateJson)
                {
                    var stagingChange = new StagingChange
                    {
                        Name = "NCR Certificate",
                        OldValue = memberDocument.NcrCertificateBlob,
                        NewValue = ncrCertificateJson
                    };

                    stagingChangeList.Add(stagingChange);
                }

                stagingChangeLog.Changes.AddRange(stagingChangeList);
            }
        }

        private void CreateALGMemberDetailsChangeLog(ALGMemberDetails memberDetails, int numberOfClients, string loanMtgSystemName, MemberStagingChangeLogResource stagingChangeLog)
        {
            if (memberDetails != null)
            {
                List<StagingChange> stagingChangeList = new List<StagingChange>();

                if (memberDetails.NumberOfClients != numberOfClients)
                {
                    var stagingChange = new StagingChange
                    {
                        Name = "Number of Clients",
                        OldValue = memberDetails.NumberOfClients.ToString(),
                        NewValue = numberOfClients.ToString()
                    };

                    stagingChangeList.Add(stagingChange);
                }
                if (memberDetails.LoanManagementSystemName != loanMtgSystemName)
                {
                    var stagingChange = new StagingChange
                    {
                        Name = "Loan Management System Name",
                        OldValue = memberDetails.LoanManagementSystemName,
                        NewValue = loanMtgSystemName
                    };

                    stagingChangeList.Add(stagingChange);
                }

                stagingChangeLog.Changes.AddRange(stagingChangeList);
            }
            else
            {
                List<StagingChange> stagingChangeList = new List<StagingChange>
                {
                    new StagingChange
                    {
                        Name = "Number of Clients",
                        OldValue = "0",
                        NewValue = numberOfClients.ToString()
                    },
                    new StagingChange
                    {
                        Name = "Loan Management System Name",
                        OldValue = "",
                        NewValue = loanMtgSystemName
                    }
                };

                stagingChangeList.AddRange(stagingChangeList);
            }
        }
        public async Task<Member> GetMember(AppDbContext _dbContext, int id)
        {
            var member = await _dbContext.Members
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == id);

            if (member != null)
                return member;

            return null;
        }
        private async Task UpdateALGLeaders(AppDbContext _dbContext, int memberId, MemberUpdateResource modelForUpdate, MemberStagingChangeLogResource stagingChangeLog, Member member)
        {
            var algLeaders = await _dbContext.ALGClientLeaders
                .Include(i => i.Leader)
                .AsNoTracking()
                .Where(i => i.ClientId == memberId)
                .ToListAsync();

            var lstExistingALGLeaders = new List<int>();
            var lstDeletedALGLeaders = new List<ALGClientLeader>();

            var newALGLeaders = new List<int>();
            if (modelForUpdate != null)
            {
                if (modelForUpdate.ALGLeaders != null)
                {
                    foreach (var leaderId in modelForUpdate.ALGLeaders)
                    {
                        //Create log for new ALG leaders
                        if (!algLeaders.Any(i => i.LeaderId == leaderId))
                        {
                            newALGLeaders.Add(leaderId);

                            var newLeader = await _dbContext.Members.FirstOrDefaultAsync(i => i.Id == leaderId);

                            var stagingChange = new StagingChange
                            {
                                Name = "ALG Leader",
                                OldValue = "",
                                NewValue = newLeader.RegisteredName
                            };

                            stagingChangeLog.Changes.Add(stagingChange);
                        }
                    }
                }

                //Create log for deleted ALG leaders

                foreach (var leader in algLeaders)
                {
                    if (!modelForUpdate.ALGLeaders.Any(i => i.Equals(leader.LeaderId)))
                    {
                        lstDeletedALGLeaders.Add(leader);

                        var stagingChange = new StagingChange
                        {
                            Name = "ALG Leader",
                            OldValue = leader.Leader.RegisteredName,
                            NewValue = ""
                        };

                        stagingChangeLog.Changes.Add(stagingChange);
                    }
                }

                //Remove access to all deleted ALG users
                foreach (var leader in lstDeletedALGLeaders)
                {
                    //Get all users of the ALG Leader that was removed
                    var algLeaderUsers = await _dbContext.MemberUsers
                        .AsNoTracking()
                        .Where(i => i.MemberId == leader.LeaderId)
                        .ToListAsync();

                    if(algLeaderUsers != null)
                    {
                        //Get all users of the ALG Leader that was removed and also linked to the client
                        var algUsersLinkedToClient = new List<MemberUsers>();
                        foreach (var user in algLeaderUsers)
                        {
                            var memberUser = _dbContext.MemberUsers
                                .FirstOrDefault(x => x.UserId == user.UserId && x.MemberId == memberId);

                            if (memberUser != null)
                            {
                                algUsersLinkedToClient.Add(memberUser);
                            }
                        }

                        if (algUsersLinkedToClient != null)
                        {
                            //Remove the ALG users from the Client
                            
                            foreach(var user in algUsersLinkedToClient)
                            {
                                var userToBeRemoved = member.Users.First(i => i.Id == user.Id);

                                if(userToBeRemoved != null)
                                {
                                    var attachedEntity = _dbContext.ChangeTracker.Entries<MemberUsers>().FirstOrDefault(e => e.Entity.Id == userToBeRemoved.Id);
                                    if (attachedEntity != null)
                                    {
                                        _dbContext.Entry(attachedEntity.Entity).State = EntityState.Detached;
                                    }

                                    //_dbContext.MemberUsers.Remove(userToBeRemoved);
                                    _dbContext.Entry(userToBeRemoved).State = EntityState.Deleted;
                                    member.Users.Remove(userToBeRemoved);
                                    await _dbContext.SaveChangesAsync();
                                }
                            }
                        }
                    }
                }

                //Delete all ALG Leaders that were removed from the update recource
                if (lstDeletedALGLeaders.Count > 0)
                {
                    _dbContext.ALGClientLeaders.RemoveRange(lstDeletedALGLeaders);
                }

                //Add new ALG leaders
                foreach (var leaderId in newALGLeaders)
                {
                    _dbContext.ALGClientLeaders.Add(new ALGClientLeader
                    {
                        ClientId = memberId,
                        LeaderId = leaderId,
                        DateCreated = DateTime.Now
                    });

                    //Give access to all new ALG users
                    var algLeaderUsers = await _dbContext.MemberUsers
                        .Include(i => i.User)
                        .Where(i => i.MemberId == leaderId && i.User.RoleId == UserRoles.ALGLeader)
                        .ToListAsync();

                    foreach(var user in algLeaderUsers)
                    {
                        _dbContext.MemberUsers.Add(new MemberUsers
                        {
                            UserId = user.UserId,
                            MemberId = memberId,
                            DateCreated = DateTime.Now
                        });
                    }
                }
            }
        }

        public async Task UpdateMemberStatus(CamundaClient camundaClient, AppDbContext _dbContext, int id, MemberStatusUpdateResource modelForUpdate, bool isSystemUser = false)
        {
            var member = new Member();
            var financialAdmin = new User();
            var sacrraAdmin = new User();

            // Fetch the member
            member = await _dbContext.Members
                .Include(x => x.MemberStatusReason)
                .FirstOrDefaultAsync(x => x.Id == id);

            MemberStagingChangeLogResource stagingChangeLog = new();

            stagingChangeLog.Changes.Add(new StagingChange
            {
                Name = "Application Status",
                OldValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)member.ApplicationStatusId).Value,
                NewValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)modelForUpdate.ApplicationStatusId).Value
            });
            stagingChangeLog.Changes.Add(new StagingChange
            {
                Name = "Status Comment",
                OldValue = (!string.IsNullOrEmpty(member.StatusComment)) ? member.StatusComment : "",
                NewValue = (!string.IsNullOrEmpty(modelForUpdate.StatusComment)) ? modelForUpdate.StatusComment : ""
            });

            if (modelForUpdate.MemberStatusReasonId > 0)
            {
                var statusReason = _dbContext.MemberStatusReasons
                    .FirstOrDefault(i => i.Id == modelForUpdate.MemberStatusReasonId);

                stagingChangeLog.Changes.Add(new StagingChange
                {
                    Name = "Member Status Reason",
                    OldValue = (member.MemberStatusReason != null) ? member.MemberStatusReason.Name : "",
                    NewValue = (statusReason != null) ? statusReason.Name : ""
                });
            }

            var updateDetailsBlob = JsonConvert.SerializeObject(member, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore });

            // Update the member model
            member.ApplicationStatusId = (modelForUpdate.ApplicationStatusId == ApplicationStatuses.MemberRegistrationActive) ? ApplicationStatuses.MemberRegistrationCompleted : modelForUpdate.ApplicationStatusId;
            member.MemberStatusReasonId = modelForUpdate.MemberStatusReasonId;
            member.StatusComment = modelForUpdate.StatusComment;


            if (member.ApplicationStatusId == ApplicationStatuses.MemberRegistrationCancelled)
            {
                member.DateCancelled = DateTime.Now;
                member.DateActivated = null;
            }
            else if (member.ApplicationStatusId == ApplicationStatuses.MemberRegistrationActive || member.ApplicationStatusId == ApplicationStatuses.MemberRegistrationCompleted)
            {
                member.DateActivated = DateTime.Now;
                member.DateCancelled = null;
            }

            // Commit changes to the database
            _dbContext.Members.Update(member);
            await _dbContext.SaveChangesAsync();

            // Fetch the financial administrator and SACRRA administrator to kick off the workflow
            financialAdmin = await _dbContext.Users.FirstOrDefaultAsync(x => x.RoleId == UserRoles.FinancialAdministrator);
            sacrraAdmin = await _dbContext.Users.FirstOrDefaultAsync(x => x.RoleId == UserRoles.SACRRAAdministrator);

            // Kick of the member status update workflow
            await AddMemberStatusUpdateTask(camundaClient, member, financialAdmin, sacrraAdmin);

            var stagingDetailsBlob = JsonConvert.SerializeObject(stagingChangeLog);

            if (stagingChangeLog.Changes.Count > 0)
            {
                var userId = 0;
                var user = await Helpers.Helpers.GetUserByAuth0Id(_dbContext, isSystemUser);
                if(user != null)
                {
                    userId = user.Id;
                }

                await Helpers.Helpers
                    .CreateEventLog(_dbContext, userId, "Member Update", member.RegisteredName, updateDetailsBlob, stagingDetailsBlob, member.Id, "Member");
            }
        }

        public async Task AddMemberStatusUpdateTask(CamundaClient camundaClient, Member member, User financialAdmin, User sacrraAdmin)
        {
            // Kick of the member update workflow
            if (member != null)
            {
                string memberStatus = "";
                if (member.ApplicationStatusId == ApplicationStatuses.MemberRegistrationCompleted)
                    memberStatus = "activated";
                else if (member.ApplicationStatusId == ApplicationStatuses.MemberRegistrationCancelled)
                    memberStatus = "cancelled";

                await camundaClient.ProcessDefinitions.ByKey("Member-Status-Update").StartProcessInstance(new StartProcessInstance()
                {
                    Variables = new Dictionary<string, VariableValue>()
                            {
                                { "memberId", VariableValue.FromObject(member.Id) },
                                { "memberStatus", VariableValue.FromObject(memberStatus) },
                                { "FinancialAdministratorAssignee", VariableValue.FromObject(financialAdmin.Id.ToString()) },
                                { "stakeHolderManagerManagerAssignee", VariableValue.FromObject(sacrraAdmin.Id.ToString()) }
                            }
                });
            }
        }
    }
}
