namespace Sacrra.Membership.Business.Helpers
{
    public class ConfigSettings
    {
        public ConfigSettings()
        {

        }

        public string ApiBaseUri { get; set; }
        public string CamundaBaseAddress { get; set; }
        public string FrontEndBaseUri { get; set; }
        public string JwtKey { get; set; }
        public string CamundaAdminUserName { get; set; }
        public string CamundaAdminPassword { get; set; }
        public string CamundaAdminEmail { get; set; }
        public string FTPServerAddress { get; set; }
        public string FTPServerPort { get; set; }
        public string FTPServerUser { get; set; }
        public string FTPServerPassword { get; set; }
        public string CamundaConnectionString { get; set; }
    }
}
