using System;

namespace Sacrra.Membership.Business
{
    /// <summary>
    /// Specifies that an entity member represents a data relationship, such as a property name in the DB.
    /// </summary>
    internal sealed class DBAssociation : Attribute
    {
        public DBAssociation() { }
        /// <summary>
        /// Gets the name of the association.
        /// </summary>
        public string Name { get; set; }
    }
}
