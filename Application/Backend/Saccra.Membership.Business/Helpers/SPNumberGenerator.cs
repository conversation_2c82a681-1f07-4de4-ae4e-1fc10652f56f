using System;
using System.Security.Cryptography;

namespace Sacrra.Membership.Business.Helpers
{
    public static class SPNumberGenerator
    {
        public static string GenerateSPNumber()
        {
            var bytes = new byte[4];
            var rng = RandomNumberGenerator.Create();
            rng.GetBytes(bytes);
            uint randomNumber = BitConverter.ToUInt32(bytes, 0) % 1000;

            string spNumber = "SP" + String.Format("{0:D4}", randomNumber);
            return spNumber;
        }
    }
}
