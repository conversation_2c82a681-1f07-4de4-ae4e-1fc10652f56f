using AutoMapper;
using Camunda.Api.Client.ExternalTask;
using Sacrra.Membership.Business.Resources;
using Sacrra.Membership.Business.Resources.Auth;
using Sacrra.Membership.Business.Resources.Camunda.Task;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Business.Resources.MemberContact;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Business.Resources.SRN;
using Sacrra.Membership.Business.Resources.MemberChanges;
using Sacrra.Membership.Business.Resources.SRNStatus;
using Sacrra.Membership.Business.Resources.BranchLocation;
using Sacrra.Membership.Business.Resources.AccountType;
using Sacrra.Membership.Business.Resources.SoftwareVendor;
using Sacrra.Membership.Business.Resources.LoanManagementSystemVendor;
using Sacrra.Membership.Business.Resources.NCRReportingAccountTypeClassification;
using Sacrra.Membership.Business.Resources.SRNContact;
using Sacrra.Membership.Business.Resources.SPGroup;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;
using Sacrra.Membership.Business.Resources.Member;
using Newtonsoft.Json;
using System.Collections.Generic;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Database.Enums;
using System;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.DTOs.TaskDTOs;
using Sacrra.Membership.Business.DTOs.SRNUpdateDTOs;
using Sacrra.Membership.Business.DTOs.MemberUpdateDTOs;
using Sacrra.Membership.Business.DTOs.SRNSummaryDTOs;
using Sacrra.Membership.Business.Resources.DocumentCategory;
using Sacrra.Membership.Business.DTOs.OSLAReasonDTOs;
using Sacrra.Membership.Business.DTOs.ReplacementFileSubmissionDTOs;
using Sacrra.Membership.Business.DTOs.AdhocFileSubmissionDTOs;
using Sacrra.Membership.Business.DTOs.AdHocFIlesDTO;
using Sacrra.Membership.Business.DTOs.AdhocRplacementScheduleDTOs;

namespace Sacrra.Membership.Business.MappingProfile
{
    public class MappingProfile : Profile
    {
        public MappingProfile()
        {
            #region Version 2 Mappings
            CreateMap<User, AuthUserResource>();

            CreateMap<SRNRequestInputDTO, SRNStatusUpdateHistory>();

            CreateMap<BranchLocationSRNRequestDTO, BranchLocation>()
                .ForMember(dest => dest.Name, opts => opts.MapFrom(src => src.Value));

            CreateMap<ReplacementFileSubmissionCategory, ReplacementFileSubmissionCategoryOutputDTO>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Name));

            CreateMap<AdhocFileSubmissionCategory, AdhocFileSubmissionCategoryOutputDTO>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Name));

            CreateMap<DocumentCategoryInputDTO, DocumentCategory>()
                .ForMember(dest => dest.Name, opts => opts.MapFrom(src => src.CategoryName));

            CreateMap<DocumentCategory, DocumentCategoryOutputDTO>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Name));

            CreateMap<ReplacementFileSubmissionReason, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Name));

            CreateMap<SRNContact, ContactOutputDTO>()
                .ForMember(dest => dest.LastName, opts => opts.MapFrom(src => src.Surname))
                .ForMember(dest => dest.OfficeNumber, opts => opts.MapFrom(src => src.OfficeTelNumber))
                .ForMember(dest => dest.CellphoneNumber, opts => opts.MapFrom(src => src.CellNumber))
                .ForMember(dest => dest.EmailAddress, opts => opts.MapFrom(src => src.Email))
                .ForMember(dest => dest.Designation, opts => opts.MapFrom(src => src.JobTitle));

            CreateMap<ContactUpdateDTO, SRNContact>()
                .ForMember(dest => dest.CellNumber, opts => opts.MapFrom(src => src.CellphoneNumber))
                .ForMember(dest => dest.OfficeTelNumber, opts => opts.MapFrom(src => src.OfficeNumber))
                .ForMember(dest => dest.ContactTypeId, opts => opts.MapFrom(src => src.ContactTypeId))
                .ForMember(dest => dest.Email, opts => opts.MapFrom(src => src.EmailAddress))
                .ForMember(dest => dest.FirstName, opts => opts.MapFrom(src => src.FirstName))
                .ForMember(dest => dest.Surname, opts => opts.MapFrom(src => src.LastName))
                .ForMember(dest => dest.JobTitle, opts => opts.MapFrom(src => src.Designation));

            CreateMap<SRN, SRNGetOutputDTO>()
                .ForMember(dest => dest.SRNDisplayName, opts => opts.MapFrom(src => src.TradingName))
                .ForMember(dest => dest.NumberOfAccounts, opts => opts.MapFrom(src => src.NumberOfActiveAccounts))
                .ForMember(dest => dest.BillingCycle, opts => opts.MapFrom(src => src.BillingCycleDay))
                .ForMember(dest => dest.LoanManagementSystemId, opts => opts.MapFrom(src => src.LoanManagementSystemVendorId))
                .ForMember(dest => dest.ThirdPartyVendorId, opts => opts.MapFrom(src => src.SoftwareVendorId))
                .ForMember(dest => dest.Contacts, opts => opts.MapFrom(src => src.Contacts))
                .ForMember(dest => dest.SPNumberId, opts => opts.MapFrom(src => src.SPGroupId))
                .ForMember(dest => dest.IsDailyFile, opts => opts.MapFrom(src => (src.FileType == SRNStatusFileTypes.DailyFile) || (src.FileType == SRNStatusFileTypes.MonthlyAndDailyFile) ? true : false))
                .ForMember(dest => dest.IsMonthlyFile, opts => opts.MapFrom(src => (src.FileType == SRNStatusFileTypes.MonthlyFile) || (src.FileType == SRNStatusFileTypes.MonthlyAndDailyFile) ? true : false));

            CreateMap<SRN, SRNViewOutputDTO>()
                .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id))
                .ForMember(dest => dest.SRNDisplayName, opts => opts.MapFrom(src => src.TradingName))
                .ForMember(dest => dest.AccountStatusDate, opts => opts.MapFrom(src => string.Format("{0:yyyy-MM-dd}", src.StatusLastUpdatedAt)))
                .ForMember(dest => dest.LastSubmissionDate, opts => opts.MapFrom(src => (src.LastSubmissionDate != null) ? string.Format("{0:yyyy-MM-dd}", src.LastSubmissionDate) : null))
                .ForMember(dest => dest.ThirdPartyVendorId, opts => opts.MapFrom(src => src.SoftwareVendorId))
                .ForMember(dest => dest.BillingCycle, opts => opts.MapFrom(src => src.BillingCycleDay))
                .ForMember(dest => dest.LoanManagementSystemId, opts => opts.MapFrom(src => src.LoanManagementSystemVendorId))
                .ForMember(dest => dest.SPNumberId, opts => opts.MapFrom(src => src.SPGroupId))
                .ForMember(dest => dest.IsDailyFile, opts => opts.MapFrom(src => (src.FileType == SRNStatusFileTypes.DailyFile) || (src.FileType == SRNStatusFileTypes.MonthlyAndDailyFile) ? true : false))
                .ForMember(dest => dest.IsMonthlyFile, opts => opts.MapFrom(src => (src.FileType == SRNStatusFileTypes.MonthlyFile) || (src.FileType == SRNStatusFileTypes.MonthlyAndDailyFile) ? true : false))
                .ForMember(dest => dest.NumberOfAccounts, opts => opts.MapFrom(src => src.NumberOfActiveAccounts))
                .ForMember(dest => dest.FileTypeId, opts => opts.MapFrom(src => src.FileType));

            CreateMap<BranchLocationSRNRequestDTO, BranchLocationUpdateResource>()
              .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Value));

            CreateMap<MemberTradingNameDTO, TradingName>();

            CreateMap<MemberTradingNameDTO, TradingNameUpdateResource>();

            CreateMap<ContactInputDTO, SRNContact>()
                .ForMember(dest => dest.Surname, opts => opts.MapFrom(src => src.LastName))
                .ForMember(dest => dest.CellNumber, opts => opts.MapFrom(src => src.CellphoneNumber))
                .ForMember(dest => dest.OfficeTelNumber, opts => opts.MapFrom(src => src.OfficeNumber))
                .ForMember(dest => dest.JobTitle, opts => opts.MapFrom(src => src.Designation))
                .ForMember(dest => dest.Email, opts => opts.MapFrom(src => src.EmailAddress));

            CreateMap<ContactInputDTO, SRNContactUpdateResource>()
                .ForMember(dest => dest.Surname, opts => opts.MapFrom(src => src.LastName))
                .ForMember(dest => dest.CellNumber, opts => opts.MapFrom(src => src.CellphoneNumber))
                .ForMember(dest => dest.OfficeTelNumber, opts => opts.MapFrom(src => src.OfficeNumber))
                .ForMember(dest => dest.JobTitle, opts => opts.MapFrom(src => src.Designation))
                .ForMember(dest => dest.Email, opts => opts.MapFrom(src => src.EmailAddress));

            CreateMap<Bureau, BureauGetDTO>();
            CreateMap<BureauLoadStatsInputDTO, BureauLoadStats>();

            CreateMap<SRNRequestInputDTO, SRNUpdateResource>()
                .ForMember(dest => dest.LoanManagementSystemVendorId, opts => opts.MapFrom(src => (src.LoanManagementSystemId > 0) ? src.LoanManagementSystemId : null))
                .ForMember(dest => dest.SoftwareVendorId, opts => opts.MapFrom(src => (src.ThirdPartyVendorId > 0) ? src.ThirdPartyVendorId : null))
                .ForMember(dest => dest.SPGroupId, opts => opts.MapFrom(src => (src.SPNumberId > 0) ? src.SPNumberId : null))
                .ForMember(dest => dest.ALGLeaderId, opts => opts.MapFrom(src => (src.ALGLeaderId > 0) ? src.ALGLeaderId : null))
                .ForMember(dest => dest.NumberOfActiveAccounts, opts => opts.MapFrom(src => src.NumberOfAccounts))
                .ForMember(dest => dest.BillingCycleDay, opts => opts.MapFrom(src => src.BillingCycle))
                .ForMember(dest => dest.TradingName, opts => opts.MapFrom(src => src.SRNDisplayName));


            CreateMap<MemberRequestInputDTO, Member>()
            .ForMember(dest => dest.SecondaryBureauId, opts => opts.MapFrom(src => (src.SecondaryBureauId > 0) ? src.SecondaryBureauId : null))
            .ForMember(dest => dest.RegisteredName, opts => opts.MapFrom(src => src.CompanyRegisteredName))
            .ForMember(dest => dest.IsSoleProp, opts => opts.MapFrom(src => src.IsSoleProprietor))
            .ForMember(dest => dest.RegisteredNumber, opts => opts.MapFrom(src => src.CompanyRegistrationNumber))
            .ForMember(dest => dest.IdNumber, opts => opts.MapFrom(src => src.IdentificationNumber))
            .ForMember(dest => dest.TradingNames, opts => opts.MapFrom(src => src.CompanyTradingNames))
            .ForMember(dest => dest.IsVatRegistrant, opts => opts.MapFrom(src => src.IsVATRegistered))
            .ForMember(dest => dest.Website, opts => opts.MapFrom(src => src.CompanyWebsite))
            .ForMember(dest => dest.HeadOfficePhysicalAddress, opts => opts.MapFrom(src => src.HeadOfficePhysicalAddress))
            .ForMember(dest => dest.HeadOfficePostalAddress, opts => opts.MapFrom(src => src.HeadOfficePostalAddress))
            .ForMember(dest => dest.IndustryClassificationId, opts => opts.MapFrom(src => src.sacrraIndustryClassId))
            .ForMember(dest => dest.IsNcrRegistrant, opts => opts.MapFrom(src => src.IsNCRRegistrant))
            .ForMember(dest => dest.NcrcpNumber, opts => opts.MapFrom(src => src.NCRCPNumber))
            .ForMember(dest => dest.NcrReportingPrimaryBusinessClassificationId, opts => opts.MapFrom(src => src.NCRReportingPrimaryBusinessClassificationId))
            .ForMember(dest => dest.Comments, opts => opts.MapFrom(src => src.MiscComments));

            CreateMap<MemberRequestInputDTO, MemberUpdateResource>()
            .ForMember(dest => dest.SecondaryBureauId, opts => opts.MapFrom(src => (src.SecondaryBureauId > 0) ? src.SecondaryBureauId : null))
            .ForMember(dest => dest.RegisteredName, opts => opts.MapFrom(src => src.CompanyRegisteredName))
            .ForMember(dest => dest.IsSoleProp, opts => opts.MapFrom(src => src.IsSoleProprietor))
            .ForMember(dest => dest.RegisteredNumber, opts => opts.MapFrom(src => src.CompanyRegistrationNumber))
            .ForMember(dest => dest.IdNumber, opts => opts.MapFrom(src => src.IdentificationNumber))
            .ForMember(dest => dest.TradingNames, opts => opts.MapFrom(src => src.CompanyTradingNames))
            .ForMember(dest => dest.IsVatRegistrant, opts => opts.MapFrom(src => src.IsVATRegistered))
            .ForMember(dest => dest.Website, opts => opts.MapFrom(src => src.CompanyWebsite))
            .ForMember(dest => dest.HeadOfficePhysicalAddress, opts => opts.MapFrom(src => src.HeadOfficePhysicalAddress))
            .ForMember(dest => dest.HeadOfficePostalAddress, opts => opts.MapFrom(src => src.HeadOfficePostalAddress))
            .ForMember(dest => dest.IndustryClassificationId, opts => opts.MapFrom(src => src.sacrraIndustryClassId))
            .ForMember(dest => dest.IsNcrRegistrant, opts => opts.MapFrom(src => src.IsNCRRegistrant))
            .ForMember(dest => dest.NcrcpNumber, opts => opts.MapFrom(src => src.NCRCPNumber))
            .ForMember(dest => dest.NcrReportingPrimaryBusinessClassificationId, opts => opts.MapFrom(src => src.NCRReportingPrimaryBusinessClassificationId))
            .ForMember(dest => dest.Comments, opts => opts.MapFrom(src => src.MiscComments));

            CreateMap<ContactInputDTO, MemberContact>()
            .ForMember(dest => dest.CellNumber, opts => opts.MapFrom(src => src.CellphoneNumber))
            .ForMember(dest => dest.OfficeTelNumber, opts => opts.MapFrom(src => src.OfficeNumber))
            .ForMember(dest => dest.ContactTypeId, opts => opts.MapFrom(src => src.ContactTypeId))
            .ForMember(dest => dest.Email, opts => opts.MapFrom(src => src.EmailAddress))
            .ForMember(dest => dest.FirstName, opts => opts.MapFrom(src => src.FirstName))
            .ForMember(dest => dest.Surname, opts => opts.MapFrom(src => src.LastName))
            .ForMember(dest => dest.JobTitle, opts => opts.MapFrom(src => src.Designation));

            CreateMap<ContactInputDTO, MemberContactUpdateResource>();

            CreateMap<SRNRequestInputDTO, SRN>()
                .ForMember(dest => dest.LoanManagementSystemVendorId, opts => opts.MapFrom(src => (src.LoanManagementSystemId > 0) ? src.LoanManagementSystemId : null))
                .ForMember(dest => dest.SoftwareVendorId, opts => opts.MapFrom(src => (src.ThirdPartyVendorId > 0) ? src.ThirdPartyVendorId : null))
                .ForMember(dest => dest.SPGroupId, opts => opts.MapFrom(src => (src.SPNumberId > 0) ? src.SPNumberId : null))
                .ForMember(dest => dest.ALGLeaderId, opts => opts.MapFrom(src => (src.ALGLeaderId > 0) ? src.ALGLeaderId : null))
                .ForMember(dest => dest.NumberOfActiveAccounts, opts => opts.MapFrom(src => src.NumberOfAccounts))
                .ForMember(dest => dest.FileType, opts => opts.MapFrom(src => CheckFileType(src)))
                .ForMember(dest => dest.TradingName, opts => opts.MapFrom(src => src.SRNDisplayName))
                .ForMember(dest => dest.BillingCycleDay, opts => opts.MapFrom(src => src.BillingCycle))
                .ForMember(dest => dest.BranchLocations, opts => opts.MapFrom(src => src.BranchLocations));

            CreateMap<ALGClientLeader, AlgLeaderClientOutputDTO>()
             .ForMember(dest => dest.CompanyName, opts => opts.MapFrom(src => src.Client.RegisteredName))
             .ForMember(dest => dest.ApplicationStatus, opts => opts.MapFrom(src => EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)src.Client.ApplicationStatusId).Value))
             .ForMember(dest => dest.CompanyRegistrationNumber, opts => opts.MapFrom(src => src.Client.RegisteredNumber))
             .ForMember(dest => dest.IdentificationNumber, opts => opts.MapFrom(src => src.Client.IdNumber))
             .ForMember(dest => dest.StakeholderManager, opts => opts.MapFrom(src => src.Client.StakeholderManager))
             .ForMember(dest => dest.MemberActivationDate, opts => opts.MapFrom(src => (src.Client.DateActivated != null) ? string.Format("{0:dd-MM-yyyy}", src.Client.DateActivated): ""));

            CreateMap<SRN, SRNSummaryDetailsOutputDTO>()
                .ForMember(dest => dest.MemberName, opts => opts.MapFrom(src => src.Member.RegisteredName))
                .ForMember(dest => dest.SRNDisplayName, opts => opts.MapFrom(src => src.TradingName))
                .ForMember(dest => dest.StakeholderManager, opts => opts.MapFrom(src => src.Member.StakeholderManager.FullName))
                .ForMember(dest => dest.ALGLeader, opts => opts.MapFrom(src => src.ALGLeader.RegisteredName))
                .ForMember(dest => dest.SPNumber, opts => opts.MapFrom(src => src.SPGroup.SPNumber))
                .ForMember(dest => dest.AccountType, opts => opts.MapFrom(src => src.AccountType.Name))
                .ForMember(dest => dest.BureauClosureDate, opts => opts.MapFrom(src => string.Format("{0:dd-MM-yyyy}", src.AccountStatusDate)))
                .ForMember(dest => dest.CreationDate, opts => opts.MapFrom(src => string.Format("{0:dd-MM-yyyy}", src.CreationDate)))
                .ForMember(dest => dest.StatusLastUpdatedAt, opts => opts.MapFrom(src => string.Format("{0:dd-MM-yyyy}", src.StatusLastUpdatedAt) == "01-01-0001" ? null : string.Format("{0:dd-MM-yyyy}", src.StatusLastUpdatedAt)))
                .ForMember(dest => dest.SRNStatus, opts => opts.MapFrom(src => src.SRNStatus.Name))
                .ForMember(dest => dest.CreditInformationClassification, opts => opts.MapFrom(src => (src.CreditInformationClassification != null)? src.CreditInformationClassification.Name : null));

            CreateMap<SRNRolloutStatusModel, SRNRolloutScheduleOutputDTO>()
                .ForMember(dest => dest.SRNDisplayName, opts => opts.MapFrom(src => src.TradingName))
                .ForMember(dest => dest.SRNStatus, opts => opts.MapFrom(src => src.RolloutStatus))
                .ForMember(dest => dest.DataContributor, opts => opts.MapFrom(src => src.RegisteredName))
                .ForMember(dest => dest.CompanyRegistrationNumber, opts => opts.MapFrom(src => src.RegisteredNumber))
                .ForMember(dest => dest.TestStartDate, opts => opts.MapFrom(src => (PopulateSRNTestStartDate(src) != null) ? string.Format("{0:dd-MM-yyyy}", DateTime.Parse(PopulateSRNTestStartDate(src))) : null))
                .ForMember(dest => dest.TestEndDate, opts => opts.MapFrom(src => (PopulateSRNTestEndDate(src) != null) ? string.Format("{0:dd-MM-yyyy}", DateTime.Parse(PopulateSRNTestEndDate(src))) : null))
                .ForMember(dest => dest.GoLiveDate, opts => opts.MapFrom(src => (PopulateSRNGoLiveDate(src) != null) ? string.Format("{0:dd-MM-yyyy}", DateTime.Parse(PopulateSRNGoLiveDate(src))) : null))
                .ForMember(dest => dest.LastUpdatedDate, opts => opts.MapFrom(src => (src.StatusLastUpdatedAt != null) ? string.Format("{0:dd-MM-yyyy}", DateTime.Parse(src.StatusLastUpdatedAt)) : null))
                .ForMember(dest => dest.SignoffDate, opts => opts.MapFrom(src => (src.SignoffDate != null) ? string.Format("{0:dd-MM-yyyy}", DateTime.Parse(src.SignoffDate)) : null));

            CreateMap<TaskListResource, ShmSRNTaskOutputDTO>()
               .ForMember(dest => dest.TaskType, opts => opts.MapFrom(src => src.TaskName))
               .ForMember(dest => dest.TaskDate, opts => opts.MapFrom(src => string.Format("{0:dd-MM-yyyy}", DateTime.Parse(src.Created))))
               .ForMember(dest => dest.Member, opts => opts.MapFrom(src => src.MemberRegisteredName))
               .ForMember(dest => dest.SRNDisplayName, opts => opts.MapFrom(src => src.TradingName))
               .ForMember(dest => dest.SRNNumber, opts => opts.MapFrom(src => src.SRNNumber))
               .ForMember(dest => dest.FileType, opts => opts.MapFrom(src => EnumHelper.GetEnumIdValuePair<SRNStatusFileTypes>((int)src.FileType)))
               .ForMember(dest => dest.FileTypeValue, opts => opts.MapFrom(src => (src.FileType == 0) ? "N/A" : EnumHelper.GetEnumIdValuePair<SRNStatusFileTypes>((int)src.FileType).Value));

            CreateMap<TaskListResource, ShmReplacementFileTaskOutputDTO>()
                .ForMember(dest => dest.TaskType, opts => opts.MapFrom(src => src.TaskName))
                .ForMember(dest => dest.TaskDate, opts => opts.MapFrom(src => string.Format("{0:dd-MM-yyyy}", DateTime.Parse(src.Created))))
                .ForMember(dest => dest.Member, opts => opts.MapFrom(src => src.MemberRegisteredName))
                .ForMember(dest => dest.SRNDisplayName, opts => opts.MapFrom(src => src.TradingName))
                .ForMember(dest => dest.SRNNumber, opts => opts.MapFrom(src => src.SRNNumber))
                .ForMember(dest => dest.FileType, opts => opts.MapFrom(src => (src.FileType == 0) ? "N/A" : EnumHelper.GetEnumIdValuePair<SRNStatusFileTypes>((int)src.FileType).Value));

            CreateMap<TaskListResource, SacrraAdminMyTasksOutputDTO>()
              .ForMember(dest => dest.TaskType, opts => opts.MapFrom(src => src.TaskName))
              .ForMember(dest => dest.TaskDate, opts => opts.MapFrom(src => string.Format("{0:dd-MM-yyyy}", DateTime.Parse(src.Created))))
              .ForMember(dest => dest.Member, opts => opts.MapFrom(src => src.MemberRegisteredName))
              .ForMember(dest => dest.SRNDisplayName, opts => opts.MapFrom(src => src.TradingName))
              .ForMember(dest => dest.MemberId, opts => opts.MapFrom(src => src.MemberId));

            CreateMap<TaskListResource, FinancialAdminMemberTasksOutputDTO>()
               .ForMember(dest => dest.TaskType, opts => opts.MapFrom(src => src.TaskName))
               .ForMember(dest => dest.TaskDate, opts => opts.MapFrom(src => string.Format("{0:dd-MM-yyyy}", DateTime.Parse(src.Created))))
               .ForMember(dest => dest.Member, opts => opts.MapFrom(src => src.MemberRegisteredName))
               .ForMember(dest => dest.StakeholderManager, opts => opts.MapFrom(src => src.StakeHolderManager));

            CreateMap<TaskListResource, GSHMShmTasksOutputDTO>()
                .ForMember(dest => dest.TaskType, opts => opts.MapFrom(src => src.TaskName))
                .ForMember(dest => dest.TaskDate, opts => opts.MapFrom(src => string.Format("{0:dd-MM-yyyy}", DateTime.Parse(src.Created))))
                .ForMember(dest => dest.Member, opts => opts.MapFrom(src => src.MemberRegisteredName))
                .ForMember(dest => dest.StakeholderManager, opts => opts.MapFrom(src => src.StakeHolderManager));

            CreateMap<Member, ALGLeaderOutputDTO>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.RegisteredName));

            CreateMap<TaskListResource, GSHMUnassignedTasksOutputDTO>()
                .ForMember(dest => dest.TaskType, opts => opts.MapFrom(src => src.TaskName))
                .ForMember(dest => dest.TaskDate, opts => opts.MapFrom(src => string.Format("{0:dd-MM-yyyy}", DateTime.Parse(src.Created))))
                .ForMember(dest => dest.Member, opts => opts.MapFrom(src => src.MemberRegisteredName))
                .ForMember(dest => dest.SacrraIndustryCategory, opts => opts.MapFrom(src => Enum.GetName(typeof(IndustryClassifications), src.SacrraIndustryCategory)))
                .ForMember(dest => dest.MembershipType, opts => opts.MapFrom(src => EnumHelper.GetEnumIdValuePair<MembershipTypes>((int)src.MembershipType).Value));

            CreateMap<TaskGetResource, ShmMemberTaskOutputDTO>()
                .ForMember(dest => dest.TaskType, opts => opts.MapFrom(src => src.Name))
                .ForMember(dest => dest.TaskDate, opts => opts.MapFrom(src => src.Created));

            CreateMap<User, AuthUserInfoOutputDTO>()
                .ForMember(dest => dest.RoleName, opts => opts.MapFrom(src => Enum.GetName(typeof(UserRoles), src.RoleId)));

            CreateMap<Member, MemberMyInfoOutputDTO>()
                .ForMember(dest => dest.MemberId, opts => opts.MapFrom(src => src.Id))
                .ForMember(dest => dest.CompanyName, opts => opts.MapFrom(src => src.RegisteredName))
                .ForMember(dest => dest.CompanyRegistrationNumber, opts => opts.MapFrom(src => src.RegisteredNumber))
                .ForMember(dest => dest.IdentificationNumber, opts => opts.MapFrom(src => src.IdNumber))
                .ForMember(dest => dest.StakeholderManager, opts => opts.MapFrom(src => (src.StakeholderManager != null) ? src.StakeholderManager.FirstName + " " + src.StakeholderManager.LastName : null))
                .ForMember(dest => dest.ApplicationStatusId, opts => opts.MapFrom(src => src.ApplicationStatusId))
                .ForMember(dest => dest.MembershipType, opts => opts.MapFrom(src => (src.MembershipTypeId > 0) ? EnumHelper.GetEnumIdValuePair<MembershipTypes>((int)src.MembershipTypeId).Value : null));

            CreateMap<TaskListResource, ShmMemberTaskOutputDTO>()
               .ForMember(dest => dest.TaskType, opts => opts.MapFrom(src => src.TaskName))
               .ForMember(dest => dest.TaskDate, opts => opts.MapFrom(src => string.Format("{0:dd-MM-yyyy}", DateTime.Parse(src.Created))))
               .ForMember(dest => dest.Member, opts => opts.MapFrom(src => src.MemberRegisteredName));

            CreateMap<SRNStatusUpdateRequestDTO, SRNStatusUpdateHistory>()
                .ForMember(dest => dest.Id, opts => opts.MapFrom(src => 0)) //Reset the ID to zero as this gets auto mapped to the SRN Id
                .ForMember(dest => dest.SRNId, opts => opts.MapFrom(src => src.Id))
                .ForMember(dest => dest.FileType, opts => opts.MapFrom(src => src.FileTypeId))
                .ForMember(dest => dest.UpdateType, opts => opts.MapFrom(src => src.UpdateTypeId));

            CreateMap<SRN, SRNUpdateInputDTO>()
                .ForMember(dest => dest.SRNDisplayName, opts => opts.MapFrom(src => src.TradingName))
                .ForMember(dest => dest.ThirdPartyVendorId, opts => opts.MapFrom(src => src.SoftwareVendorId));

            CreateMap<SRNSummaryAllDetailsOutputDTO, SRNSummaryAllDetailsBureauOutputDTO>();

            CreateMap<BranchLocationUpdateResource, BranchLocationOutputDTO>();
            CreateMap<ContactUpdateDTO, ContactOutputDTO>();

            CreateMap<SRNUpdateInputDTO, SRNGetOutputDTO>();

            CreateMap<SRNContact, ContactUpdateDTO>();

            CreateMap<DWException, DWExceptionOutputDTO>();
            CreateMap<DWExceptionOutputDTO, DWExceptionTaskItemDTO>()
                .ForMember(dest => dest.ExceptionDateTime, opts => opts.MapFrom(src => src.TransactionDate));
            CreateMap<MailStorageDTO, MailStorage>();
            CreateMap<MailDeliveryStatusDTO, MailDeliveryStatus>();
            CreateMap<MailFlagsDTO, MailFlag>();
            CreateMap<MailEnvelopeDTO, MailEnvelop>();
            CreateMap<MailHeadersDTO, MailHeader>();
            CreateMap<MailMessageDTO, MailgunMessage>()
                .ForMember(dest => dest.MailHeader, opts => opts.MapFrom(src => src.Headers));
            CreateMap<MailItemDTO, MailItem>()
                .ForMember(dest => dest.MailFlag, opts => opts.MapFrom(src => src.Flags))
                .ForMember(dest => dest.MailDeliveryStatus, opts => opts.MapFrom(src => src.DeliveryStatus))
                .ForMember(dest => dest.MailEnvelop, opts => opts.MapFrom(src => src.Envelope))
                .ForMember(dest => dest.MailgunMessage, opts => opts.MapFrom(src => src.Message))
                .ForMember(dest => dest.MailStorage, opts => opts.MapFrom(src => src.Storage));
            CreateMap<MailPagingDTO, MailPaging>();
            CreateMap<MailAttachmentDTO, MailAttachment>();
            CreateMap<MailEventDTO, MailEvent>()
                .ForMember(dest => dest.MailItems, opts => opts.MapFrom(src => src.Items))
                .ForMember(dest => dest.MailPaging, opts => opts.MapFrom(src => src.Paging));

            CreateMap<MailItemDTO, MailgunEmailContentDTO>()
                .ForMember(dest => dest.DeliveryStatusAttemptNo, opts => opts.MapFrom(src => (src.DeliveryStatus != null) ? src.DeliveryStatus.AttemptNo : 0))
                .ForMember(dest => dest.DeliveryStatusCode, opts => opts.MapFrom(src => (src.DeliveryStatus != null) ? src.DeliveryStatus.Code : 0))
                .ForMember(dest => dest.DeliveryStatusDescription, opts => opts.MapFrom(src => (src.DeliveryStatus != null) ? src.DeliveryStatus.Description : null))
                .ForMember(dest => dest.DeliveryStatusMessage, opts => opts.MapFrom(src => (src.DeliveryStatus != null) ? src.DeliveryStatus.Message : null))
                .ForMember(dest => dest.DeliveryStatusSessionSeconds, opts => opts.MapFrom(src => (src.DeliveryStatus != null) ? src.DeliveryStatus.SessionSeconds : 0))
                .ForMember(dest => dest.EnvelopSender, opts => opts.MapFrom(src => (src.Envelope != null) ? src.Envelope.Sender : null))
                .ForMember(dest => dest.EnvelopSendingIp, opts => opts.MapFrom(src => (src.Envelope != null) ? src.Envelope.SendingIp : null))
                .ForMember(dest => dest.EnvelopTargets, opts => opts.MapFrom(src => (src.Envelope != null) ? src.Envelope.Targets : null))
                .ForMember(dest => dest.EnvelopTransport, opts => opts.MapFrom(src => (src.Envelope != null) ? src.Envelope.Transport : null))
                .ForMember(dest => dest.HeaderFrom, opts => opts.MapFrom(src => (src.Message != null) ? (src.Message.Headers != null) ? src.Message.Headers.From : null : null))
                .ForMember(dest => dest.HeaderMessageId, opts => opts.MapFrom(src => (src.Message != null) ? (src.Message.Headers != null) ? src.Message.Headers.MessageId : null : null))
                .ForMember(dest => dest.HeaderSubject, opts => opts.MapFrom(src => (src.Message != null) ? (src.Message.Headers != null) ? src.Message.Headers.Subject : null : null))
                .ForMember(dest => dest.HeaderTo, opts => opts.MapFrom(src => (src.Message != null) ? (src.Message.Headers != null) ? src.Message.Headers.To : null : null))
                .ForMember(dest => dest.MessageSize, opts => opts.MapFrom(src => (src.Message != null) ? src.Message.Size : 0))
                .ForMember(dest => dest.Attachments, opts => opts.MapFrom(src => (src.Message != null) ? (src.Message.Attachments != null) ? src.Message.Attachments : null : null));

            CreateMap<MemberDomain, MemberDomainOutputDTO>();
            CreateMap<MemberDomainInputDTO, MemberDomain>();
            CreateMap<MemberDomainUpdateDTO, MemberDomain>();
            CreateMap<Member, ReportFilterAlgLeaderDTO>()
                .ForMember(dest => dest.AlgName, opts => opts.MapFrom(src => src.RegisteredName))
                .ForMember(dest => dest.Members, opts => opts.MapFrom(src => PopulateALGLeaderClients(src.Clients)));

            CreateMap<CreditInformationClassification, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Name));

            CreateMap<BureauObscureMapping, BureauObscureMappingOutputDTO>()
                .ForMember(dest => dest.RegisteredName, opts => opts.MapFrom(src => src.Member.RegisteredName));

            CreateMap<DocumentCategory, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Name));

            CreateMap<DocumentStatus, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Name));

            CreateMap<MonthlyOSLAReason, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Name));

            CreateMap<SRNMonthlyOSLAReason, OSLAReasonInputDTO>();
            CreateMap<OSLAReasonInputDTO, SRNMonthlyOSLAReason>();

            CreateMap<Document, DocumentOutputDTO>();
            CreateMap<DocumentInputDTO, Document>();
            CreateMap<Document, DocumentOutputSingleDTO>()
                .ForMember(dest => dest.LastUpdatedAt, opts => opts.MapFrom(src => string.Format("{0:yyyy-MM-dd}", src.LastUpdatedAt)));

            CreateMap<ReplacementFileSubmissionReason, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Name));

            CreateMap<AdhocFileSubmissionReason, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Name));

            CreateMap<ReplacementFileSubmissionCategory, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Name));

            CreateMap<AdhocFileSubmissionCategory, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Name));

            #endregion

            #region Version 1 Mappings
            CreateMap<UserCreateResource, User>();
            CreateMap<User, UserGetResource>()
                 .ForMember(dest => dest.RoleName, opts => opts.MapFrom(src => Enum.GetName(typeof(UserRoles), src.RoleId)));

            CreateMap<User, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.FullName));

            CreateMap<ALGClientGetResource, AlgLeaderClientOutputDTO>()
                .ForMember(dest => dest.IdentificationNumber, opts => opts.MapFrom(src => src.Client.IdNumber))
                .ForMember(dest => dest.CompanyName, opts => opts.MapFrom(src => src.Client.RegisteredName))
                .ForMember(dest => dest.CompanyRegistrationNumber, opts => opts.MapFrom(src => src.Client.RegisteredNumber))
                .ForMember(dest => dest.StakeholderManager, opts => opts.MapFrom(src => src.Client.ShmName))
                .ForMember(dest => dest.ApplicationStatus, opts => opts.MapFrom(src => src.Client.ApplicationStatus))
                .ForMember(dest => dest.ApplicationStatusId, opts => opts.MapFrom(src => src.Client.ApplicationStatusId))
                .ForMember(dest => dest.ClientId, opts => opts.MapFrom(src => src.Client.Id))
                .ForMember(dest => dest.MemberActivationDate, opts => opts.MapFrom(src => src.Client.DateActivated != ""? src.Client.DateActivated: null));

            CreateMap<BranchLocation, BranchLocationOutputDTO>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Name));

            CreateMap<SRNRolloutStatusResource, SRNRolloutStatusResource>()
                .ForMember(dest => dest.StatusLastUpdatedAt, opts => opts.MapFrom(src => src.StatusLastUpdatedAt == "01/01/0001 00:00:00" ? "" : src.StatusLastUpdatedAt));

            CreateMap<Resources.Bureau.BureauCreateResource, Bureau>();
            CreateMap<Resources.Bureau.BureauUpdateResource, Bureau>();
            CreateMap<Bureau, Resources.Bureau.BureauGetResource>();
            CreateMap<Bureau, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Name));

            CreateMap<TradingNameCreateResource, TradingName>();
            CreateMap<TradingNameUpdateResource, TradingName>();
            CreateMap<TradingName, TradingNameGetResource>();
            CreateMap<TradingName, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Name));
            CreateMap<TradingNameCreateResource, TradingNameUpdateResource>();

            CreateMap<MemberCreateResource, Member>()
                .ForPath(dest => dest.SecondaryBureauId, opts => opts.MapFrom(src => (src.SecondaryBureauId > 0) ? src.SecondaryBureauId : null));

            CreateMap<MemberUpdateResource, Member>()
                .ForMember(dest => dest.DisqualificationReason, opts => opts.MapFrom(src => src.RejectReason));

            CreateMap<MemberUpdateInputDTO, Member>()
                .ForMember(dest => dest.DisqualificationReason, opts => opts.MapFrom(src => src.RejectReason))
                .ForMember(dest => dest.RegisteredName, opts => opts.MapFrom(src => src.CompanyRegisteredName))
                .ForMember(dest => dest.RegisteredNumber, opts => opts.MapFrom(src => src.CompanyRegistrationNumber))
                .ForMember(dest => dest.IdNumber, opts => opts.MapFrom(src => src.IdentificationNumber))
                .ForMember(dest => dest.TradingNames, opts => opts.MapFrom(src => src.CompanyTradingNames))
                .ForMember(dest => dest.IsVatRegistrant, opts => opts.MapFrom(src => src.IsVatRegistered))
                .ForMember(dest => dest.Website, opts => opts.MapFrom(src => src.CompanyWebsite))
                .ForMember(dest => dest.IndustryClassificationId, opts => opts.MapFrom(src => src.SacrraIndustryClassId))
                .ForMember(dest => dest.Comments, opts => opts.MapFrom(src => src.MiscComments))
                .ForMember(dest => dest.IsSoleProp, opts => opts.MapFrom(src => src.IsSoleProprietor));

            CreateMap<Member, MemberUpdateInputDTO>()
                .ForMember(dest => dest.RejectReason, opts => opts.MapFrom(src => src.DisqualificationReason))
                .ForMember(dest => dest.CompanyRegisteredName, opts => opts.MapFrom(src => src.RegisteredName))
                .ForMember(dest => dest.CompanyRegistrationNumber, opts => opts.MapFrom(src => src.RegisteredNumber))
                .ForMember(dest => dest.IdentificationNumber, opts => opts.MapFrom(src => src.IdNumber))
                .ForMember(dest => dest.CompanyTradingNames, opts => opts.MapFrom(src => src.TradingNames))
                .ForMember(dest => dest.IsVatRegistered, opts => opts.MapFrom(src => src.IsVatRegistrant))
                .ForMember(dest => dest.CompanyWebsite, opts => opts.MapFrom(src => src.Website))
                .ForMember(dest => dest.SacrraIndustryClassId, opts => opts.MapFrom(src => src.IndustryClassificationId))
                .ForMember(dest => dest.MiscComments, opts => opts.MapFrom(src => src.Comments))
                .ForMember(dest => dest.CompanyTradingNames, opts => opts.MapFrom(src => src.TradingNames))
                .ForMember(dest => dest.IsSoleProprietor, opts => opts.MapFrom(src => src.IsSoleProp));

            CreateMap<MemberContact, MemberContactUpdateResource>()
                .ForMember(dest => dest.CellphoneNumber, opts => opts.MapFrom(src => src.CellNumber))
                .ForMember(dest => dest.Designation, opts => opts.MapFrom(src => src.JobTitle))
                .ForMember(dest => dest.EmailAddress, opts => opts.MapFrom(src => src.Email))
                .ForMember(dest => dest.LastName, opts => opts.MapFrom(src => src.Surname))
                .ForMember(dest => dest.OfficeNumber, opts => opts.MapFrom(src => src.OfficeTelNumber));

            CreateMap<TradingName, TradingNameUpdateResource>();

            CreateMap<Member, MemberGetResource>()
                .ForPath(dest => dest.MembershipType.Id, opts => opts.MapFrom(src => src.MembershipTypeId))
                .ForPath(dest => dest.ApplicationStatus.Id, opts => opts.MapFrom(src => src.ApplicationStatusId))
                .ForPath(dest => dest.PrincipleDebtRange.Id, opts => opts.MapFrom(src => (src.PrincipleDebtRangeId > 0) ? src.PrincipleDebtRangeId : 0))
                .ForPath(dest => dest.PrincipleDebtRange.Value, opts => opts.MapFrom(src => (src.PrincipleDebtRangeId > 0) ? src.PrincipleDebtRangeId.ToString() : ""))
                .ForPath(dest => dest.IndustryClassification.Id, opts => opts.MapFrom(src => (src.IndustryClassificationId > 0) ? src.IndustryClassificationId : 0))
                .ForPath(dest => dest.IndustryClassification.Value, opts => opts.MapFrom(src => (src.IndustryClassificationId > 0) ? EnumHelper.GetEnumIdValuePair<IndustryClassifications>((int)src.IndustryClassificationId).Value : ""))
                .ForPath(dest => dest.NcrReportingPrimaryBusinessClassification.Id, opts => opts.MapFrom(src => (src.NcrReportingPrimaryBusinessClassificationId > 0) ? src.NcrReportingPrimaryBusinessClassificationId : 0))
                .ForPath(dest => dest.NcrReportingPrimaryBusinessClassification.Value, opts => opts.MapFrom(src => (src.NcrReportingPrimaryBusinessClassificationId > 0) ? src.NcrReportingPrimaryBusinessClassificationId.ToString() : ""))
                .ForMember(dest => dest.ChangeRequestStatus, opts => opts.MapFrom(src => src.ChangeRequest.Status))
                .ForPath(dest => dest.StakeholderManager.Id, opts => opts.MapFrom(src => (src.StakeholderManager == null) ? 0 : src.StakeholderManager.Id))
                .ForPath(dest => dest.StakeholderManager.Value, opts => opts.MapFrom(src => (src.StakeholderManager == null) ? "" : src.StakeholderManager.FirstName + " " + src.StakeholderManager.LastName))
                .ForMember(dest => dest.NcrCategory, opts => opts.MapFrom(src => src.NcrCategory ?? ""))
                .ForPath(dest => dest.PrimaryBureau.Id, opts => opts.MapFrom(src => (src.PrimaryBureauId > 0) ? src.PrimaryBureauId : 0))
                .ForPath(dest => dest.PrimaryBureau.Value, opts => opts.MapFrom(src => (src.PrimaryBureau != null) ? src.PrimaryBureau.RegisteredName : ""))
                .ForPath(dest => dest.SecondaryBureau.Id, opts => opts.MapFrom(src => (src.SecondaryBureauId > 0) ? src.SecondaryBureauId : 0))
                .ForPath(dest => dest.SecondaryBureau.Value, opts => opts.MapFrom(src => (src.SecondaryBureau != null) ? src.SecondaryBureau.RegisteredName : ""))
                .ForPath(dest => dest.DisqualificationReason, opts => opts.MapFrom(src => src.DisqualificationReason ?? ""))
                .ForPath(dest => dest.Website, opts => opts.MapFrom(src => src.Website ?? ""))
                .ForPath(dest => dest.HeadOfficePostalAddress, opts => opts.MapFrom(src => src.HeadOfficePostalAddress ?? ""))
                .ForPath(dest => dest.HeadOfficePhysicalAddress, opts => opts.MapFrom(src => src.HeadOfficePhysicalAddress ?? ""))
                .ForPath(dest => dest.AnalyticsCompanyName, opts => opts.MapFrom(src => src.AnalyticsCompanyName ?? ""))
                .ForPath(dest => dest.VatNumber, opts => opts.MapFrom(src => src.VatNumber ?? ""))
                .ForPath(dest => dest.IdNumber, opts => opts.MapFrom(src => src.IdNumber ?? ""));

            CreateMap<MemberCreateResource, MemberUpdateResource>();

            CreateMap<Member, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.RegisteredName));
            CreateMap<Member, MemberUpdateResource>();
            CreateMap<MemberUpdateResource, MemberGetResource>();

            string[] activeStatuses = new string[] { "Live", "Live - Missing information",
                        "Test - DTH user info to be updated", "Sale In Progress - Partial",
                        "Sale In Progress - Full", "Split In Progress - Partial", "Split In Progress - Full",
                        "Merge In Progress"};

            CreateMap<Member, MemberGetCustomResource>()
                .ForPath(dest => dest.StakeholderManager.Id, opts => opts.MapFrom(src => (src.StakeholderManager == null) ? 0 : src.StakeholderManager.Id))
                .ForPath(dest => dest.StakeholderManager.Value, opts => opts.MapFrom(src => (src.StakeholderManager == null) ? "" : src.StakeholderManager.FirstName + " " + src.StakeholderManager.LastName))
                .ForPath(dest => dest.MemberType, opts => opts.MapFrom(src => ((src.MembershipTypeId > 0) ? EnumHelper.GetEnumIdValuePair<MembershipTypes>((int)src.MembershipTypeId).Value : "")))
                .ForPath(dest => dest.ShmName, opts => opts.MapFrom(src => src.StakeholderManager.FullName))
                .ForPath(dest => dest.ApplicationStatus, opts => opts.MapFrom(src => ((src.ApplicationStatusId > 0) ? EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)src.ApplicationStatusId).Value : "")))
                .ForMember(dest => dest.DateActivated, opts => opts.MapFrom(src => (src.DateActivated != null) ? string.Format("{0:dd-MM-yyyy}", src.DateActivated) : ""));

            CreateMap<Member, MemberGetSimpleResource>()
                .ForPath(dest => dest.StakeholderManager, opts => opts.MapFrom(src => (src.StakeholderManager == null) ? "" : src.StakeholderManager.FirstName + " " + src.StakeholderManager.LastName))
                .ForPath(dest => dest.MembershipType, opts => opts.MapFrom(src => ((src.MembershipTypeId > 0) ? EnumHelper.GetEnumIdValuePair<MembershipTypes>((int)src.MembershipTypeId).Value : "")))
                .ForPath(dest => dest.ApplicationStatus, opts => opts.MapFrom(src => ((src.ApplicationStatusId > 0) ? EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)src.ApplicationStatusId).Value : "")));
                

            CreateMap<ExternalTaskQuery, TaskGetResource>();
            CreateMap<TaskFilterResource, ExternalTaskQuery>();

            CreateMap<User, UserTaskListGetResource>()
                .ForMember(dest => dest.UserName, opts => opts.MapFrom(src => src.FullName))
                .ForMember(dest => dest.Role, opts => opts.MapFrom(src => src.RoleId.ToString()))
                .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Id.ToString()));

            CreateMap<TaskGetResource, TaskListResource>()
                .ForMember(dest => dest.TaskName, opts => opts.MapFrom(src => src.Name))
                .ForMember(dest => dest.MemberRegisteredName, opts => opts.MapFrom(src => src.RegisteredName));

            CreateMap<MemberContactCreateResource, MemberContactUpdateResource>();
            CreateMap<MemberContactCreateResource, MemberContact>();
            CreateMap<MemberContactUpdateResource, MemberContact>()
                .ForPath(dest => dest.Surname, opts => opts.MapFrom(src => src.LastName))
                .ForPath(dest => dest.JobTitle, opts => opts.MapFrom(src => src.Designation))
                .ForPath(dest => dest.OfficeTelNumber, opts => opts.MapFrom(src => src.OfficeNumber))
                .ForPath(dest => dest.CellNumber, opts => opts.MapFrom(src => src.CellphoneNumber))
                .ForPath(dest => dest.Email, opts => opts.MapFrom(src => src.EmailAddress));
            CreateMap<MemberContact, MemberContactGetResource>();
            CreateMap<MemberCreateResource, PartialMember>();
            CreateMap<MemberUpdateResource, PartialMember>()
                .ForMember(dest => dest.DisqualificationReason, opts => opts.MapFrom(src => src.RejectReason));

            CreateMap<PartialMember, MemberGetResource>()
                .ForPath(dest => dest.MembershipType.Id, opts => opts.MapFrom(src => src.MembershipTypeId))
                .ForPath(dest => dest.ApplicationStatus.Id, opts => opts.MapFrom(src => src.ApplicationStatusId))
                .ForPath(dest => dest.PrincipleDebtRange.Id, opts => opts.MapFrom(src => src.PrincipleDebtRangeId))
                .ForPath(dest => dest.IndustryClassification.Id, opts => opts.MapFrom(src => src.IndustryClassificationId))
                .ForPath(dest => dest.NcrReportingPrimaryBusinessClassification.Id, opts => opts.MapFrom(src => src.NcrReportingPrimaryBusinessClassificationId));

            CreateMap<PartialMember, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.RegisteredName));

            CreateMap<ContactTypeCreateResource, ContactType>();
            CreateMap<ContactTypeUpdateResource, ContactType>();
            CreateMap<ContactType, ContactTypeGetResource>();
            CreateMap<ContactType, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Name));

            CreateMap<MemberApplicationChangePostResource, EventLog>();
            CreateMap<EventLog, MemberApplicationChangeGetResource>();

            CreateMap<SRNCreateResource, SRN>()
                .ForMember(dest => dest.LoanManagementSystemVendorId, opts => opts.MapFrom(src => (src.LoanManagementSystemVendorId > 0) ? src.LoanManagementSystemVendorId : null))
                .ForMember(dest => dest.SoftwareVendorId, opts => opts.MapFrom(src => (src.SoftwareVendorId > 0) ? src.SoftwareVendorId : null))
                .ForMember(dest => dest.SPGroupId, opts => opts.MapFrom(src => (src.SPGroupId > 0) ? src.SPGroupId : null))
                .ForMember(dest => dest.ALGLeaderId, opts => opts.MapFrom(src => (src.ALGLeaderId > 0) ? src.ALGLeaderId : null))
                .ForMember(dest => dest.SRNStatusUpdates, opts => opts.MapFrom(src => CreateSRNStatusUpdates(src)));

            CreateMap<SRNUpdateResource, SRN>()
                .ForMember(dest => dest.LoanManagementSystemVendorId, opts => opts.MapFrom(src => (src.LoanManagementSystemVendorId > 0) ? src.LoanManagementSystemVendorId : null))
                .ForMember(dest => dest.SoftwareVendorId, opts => opts.MapFrom(src => (src.SoftwareVendorId > 0) ? src.SoftwareVendorId : null))
                .ForMember(dest => dest.SPGroupId, opts => opts.MapFrom(src => (src.SPGroupId > 0) ? src.SPGroupId : null));

            CreateMap<SRNUpdateInputDTO, SRN>()
                .ForMember(dest => dest.BillingCycleDay, opts => opts.MapFrom(src => src.BillingCycle))
                .ForMember(dest => dest.NumberOfActiveAccounts, opts => opts.MapFrom(src => src.NumberOfAccounts))
                .ForMember(dest => dest.TradingName, opts => opts.MapFrom(src => src.SRNDisplayName))
                .ForMember(dest => dest.SoftwareVendorId, opts => opts.MapFrom(src => (src.ThirdPartyVendorId > 0) ? src.ThirdPartyVendorId : null))
                .ForMember(dest => dest.LoanManagementSystemVendorId, opts => opts.MapFrom(src => (src.LoanManagementSystemId > 0) ? src.LoanManagementSystemId : null))
                .ForMember(dest => dest.SoftwareVendorId, opts => opts.MapFrom(src => (src.ThirdPartyVendorId > 0) ? src.ThirdPartyVendorId : null))
                .ForMember(dest => dest.SPGroupId, opts => opts.MapFrom(src => (src.SPNumberId > 0) ? src.SPNumberId : null));

            CreateMap<SRN, SRNGetResource>()
                .ForMember(dest => dest.StakeholderManager, opts => opts.MapFrom(src => src.Member.StakeholderManager))
                .ForMember(dest => dest.ChangeRequestStatus, opts => opts.MapFrom(src => src.ChangeRequest.Status))
                .ForMember(dest => dest.LastSubmissionDate, opts => opts.MapFrom(src => (src.LastSubmissionDate != null) ? string.Format("{0:yyyy-MM-dd}", src.LastSubmissionDate) : "0001-01-01T00:00:00"));

            CreateMap<SRN, SRNGetV2Resource>()
                .ForMember(dest => dest.StakeholderManager, opts => opts.MapFrom(src => src.Member.StakeholderManager))
                .ForMember(dest => dest.ChangeRequestStatus, opts => opts.MapFrom(src => src.ChangeRequest.Status))
                .ForMember(dest => dest.LastSubmissionDate, opts => opts.MapFrom(src => (src.LastSubmissionDate != null) ? string.Format("{0:yyyy-MM-dd}", src.LastSubmissionDate) : "0001-01-01T00:00:00"));

            CreateMap<SRN, SRNSummaryResource>()
                .ForMember(dest => dest.StakeholderManager, opts => opts.MapFrom(src => src.Member.StakeholderManager))
                .ForMember(dest => dest.ChangeRequestStatus, opts => opts.MapFrom(src => src.ChangeRequest.Status))
                .ForMember(dest => dest.BureauClosureDate, opts => opts.MapFrom(src => src.AccountStatusDate));
            CreateMap<SRN, SRNSummaryResourceSimple>()
                .ForMember(dest => dest.StakeholderManager, opts => opts.MapFrom(src => src.Member.StakeholderManager))
                .ForMember(dest => dest.BureauClosureDate, opts => opts.MapFrom(src => src.AccountStatusDate));
            CreateMap<SRN, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.SRNNumber));
            CreateMap<SRNContact, SRNContactUpdateResource>();
            CreateMap<SRNCreateResource, SRNUpdateResource>()
                .ForMember(dest => dest.LoanManagementSystemVendorId, opts => opts.MapFrom(src => (src.LoanManagementSystemVendorId > 0) ? src.LoanManagementSystemVendorId : null))
                .ForMember(dest => dest.SoftwareVendorId, opts => opts.MapFrom(src => (src.SoftwareVendorId > 0) ? src.SoftwareVendorId : null))
                .ForMember(dest => dest.SPGroupId, opts => opts.MapFrom(src => (src.SPGroupId > 0) ? src.SPGroupId : null))
                .ForMember(dest => dest.ALGLeaderId, opts => opts.MapFrom(src => (src.ALGLeaderId > 0) ? src.ALGLeaderId : null));

            CreateMap<SRNStatusCreateResource, SRNStatus>();
            CreateMap<SRNStatusUpdateResource, SRNStatus>();
            CreateMap<SRNStatus, SRNStatusGetResource>();
            CreateMap<SRNStatus, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Name));

            CreateMap<BranchLocationCreateResource, BranchLocation>();
            CreateMap<BranchLocationSRNCreateResource, BranchLocation>();
            CreateMap<BranchLocationUpdateResource, BranchLocation>()
                .ForMember(dest => dest.Name, opts => opts.MapFrom(src => src.Value)).ReverseMap();
            CreateMap<BranchLocation, BranchLocationGetResource>();
            CreateMap<BranchLocation, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Name));

            CreateMap<BranchLocationSRNCreateResource, BranchLocationUpdateResource>();

            CreateMap<AccountTypeCreateResource, AccountType>();
            CreateMap<AccountTypeUpdateResource, AccountType>();
            CreateMap<AccountType, AccountTypeGetResource>();
            CreateMap<AccountType, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Name));

            CreateMap<SoftwareVendorCreateResource, SoftwareVendor>();
            CreateMap<SoftwareVendorUpdateResource, SoftwareVendor>();
            CreateMap<SoftwareVendor, SoftwareVendorGetResource>();
            CreateMap<SoftwareVendor, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Name));

            CreateMap<LoanManagementSystemVendorCreateResource, LoanManagementSystemVendor>();
            CreateMap<LoanManagementSystemVendorUpdateResource, LoanManagementSystemVendor>();
            CreateMap<LoanManagementSystemVendor, LoanManagementSystemVendorGetResource>();
            CreateMap<LoanManagementSystemVendor, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Name));

            CreateMap<ALGCreateResource, ALG>();
            CreateMap<ALGUpdateResource, ALG>();
            CreateMap<ALG, ALGGetResource>();
            CreateMap<ALG, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Name));

            CreateMap<NCRReportingAccountTypeClassificationCreateResource, NCRReportingAccountTypeClassification>();
            CreateMap<NCRReportingAccountTypeClassificationUpdateResource, NCRReportingAccountTypeClassification>();
            CreateMap<NCRReportingAccountTypeClassification, NCRReportingAccountTypeClassificationGetResource>();
            CreateMap<NCRReportingAccountTypeClassification, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Name));
            CreateMap<SRNContactCreateResource, SRNContact>();
            CreateMap<SRNContactUpdateResource, SRNContact>();
            CreateMap<SRNContact, SRNContactGetResource>();
            CreateMap<SRNContactCreateResource, SRNContactUpdateResource>();
            CreateMap<SRNContact, MemberContactUpdateResource>();

            CreateMap<SPGroupCreateResource, SPGroup>();
            CreateMap<SPGroupUpdateResource, SPGroup>();
            CreateMap<SPGroup, SPGroupGetResource>();
            CreateMap<SPGroup, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.SPNumber));

            CreateMap<IdValuePairResource, MemberStatusReason>();
            CreateMap<MemberStatusReason, IdValuePairResource>();
            CreateMap<MemberStatusReason, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Name));

            CreateMap<MemberUpdateResource, ChangeRequestStaging>();
            CreateMap<MemberUpdateInputDTO, ChangeRequestStaging>();
            CreateMap<SRNUpdateInputDTO, ChangeRequestStaging>();
            CreateMap<ChangeRequestStaging, Member>();
            CreateMap<ChangeRequestStaging, MemberChangeRequestGetResource>();

            CreateMap<EventLog, EventLogPrepareGetResource>()
                .ForMember(dest => dest.ChangeLog, opts => opts.MapFrom(src => JsonConvert.DeserializeObject<MemberStagingChangeLogResource>(src.ChangeBlob)));

            CreateMap<EventLogPrepareGetResource, EventLogGetResource>()
                .ForMember(dest => dest.Changes, opts => opts.MapFrom(src => src.ChangeLog.Changes));

            CreateMap<UserCreateSimpleResource, UserCreateResource>();

            CreateMap<SRNUpdateResource, ChangeRequestStaging>();

            CreateMap<AffiliateCreateResource, MemberCreateResource>();
            CreateMap<AffiliateCreateResource, Member>();
            CreateMap<MemberGetResource, AffiliateGetResource>();
            CreateMap<AffiliateUpdateResource, MemberUpdateResource>();

            CreateMap<ALGLeaderCreateResource, MemberCreateResource>();
            CreateMap<TaskListResource, ShmAdhocFileTaskOutputDTO>()
            .ForMember(dest => dest.TaskType, opts => opts.MapFrom(src => src.TaskName))
            .ForMember(dest => dest.TaskDate, opts => opts.MapFrom(src => string.Format("{0:dd-MM-yyyy}", DateTime.Parse(src.Created))))
            .ForMember(dest => dest.Member, opts => opts.MapFrom(src => src.MemberRegisteredName))
            .ForMember(dest => dest.SRNDisplayName, opts => opts.MapFrom(src => src.TradingName))
            .ForMember(dest => dest.SRNNumber, opts => opts.MapFrom(src => src.SRNNumber))
            .ForMember(dest => dest.FileType, opts => opts.MapFrom(src => EnumHelper.GetEnumIdValuePair<SRNStatusFileTypes>((int)src.FileType)));

            CreateMap<MemberGetResource, ALGLeaderGetResource>();
            CreateMap<ALGClientLeader, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Client.RegisteredName))
                .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.ClientId));
            CreateMap<ALGLeaderUpdateResource, MemberUpdateResource>();

            CreateMap<Resources.BureauCreateResource, MemberCreateResource>();
            CreateMap<MemberGetResource, Resources.BureauGetResource>();
            CreateMap<Resources.BureauUpdateResource, MemberUpdateResource>();

            CreateMap<MemberUpdateResource, MemberUpdateAllTypesResource>();
            CreateMap<AffiliateUpdateResource, MemberUpdateAllTypesResource>();
            CreateMap<ALGLeaderUpdateResource, MemberUpdateAllTypesResource>();
            CreateMap<Resources.BureauUpdateResource, MemberUpdateAllTypesResource>();

            CreateMap<Member, MemberUpdateAllTypesResource>();

            CreateMap<MemberUpdateAllTypesResource, Member>();

            CreateMap<ALGClientGetResource, MemberGetCustomResource>()
                .ForMember(dest => dest.AnalyticsCompanyName, opts => opts.MapFrom(src => src.Client.AnalyticsCompanyName))
                .ForMember(dest => dest.AnnualTurnover, opts => opts.MapFrom(src => src.Client.AnnualTurnover))
                .ForMember(dest => dest.ApplicationStatusId, opts => opts.MapFrom(src => src.Client.ApplicationStatusId))
                .ForMember(dest => dest.Comments, opts => opts.MapFrom(src => src.Client.Comments))
                .ForMember(dest => dest.DisqualificationReason, opts => opts.MapFrom(src => src.Client.DisqualificationReason))
                .ForMember(dest => dest.HeadOfficePhysicalAddress, opts => opts.MapFrom(src => src.Client.HeadOfficePhysicalAddress))
                .ForMember(dest => dest.HeadOfficePostalAddress, opts => opts.MapFrom(src => src.Client.HeadOfficePostalAddress))
                .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Client.Id))
                .ForMember(dest => dest.IdNumber, opts => opts.MapFrom(src => src.Client.IdNumber))
                .ForMember(dest => dest.IsNcrRegistrant, opts => opts.MapFrom(src => src.Client.IsNcrRegistrant))
                .ForMember(dest => dest.IsSoleProp, opts => opts.MapFrom(src => src.Client.IsSoleProp))
                .ForMember(dest => dest.IsVatRegistrant, opts => opts.MapFrom(src => src.Client.IsVatRegistrant))
                .ForMember(dest => dest.MembershipTypeId, opts => opts.MapFrom(src => src.Client.MembershipTypeId))
                .ForMember(dest => dest.NcrCategory, opts => opts.MapFrom(src => src.Client.NcrCategory))
                .ForMember(dest => dest.IndustryClassificationId, opts => opts.MapFrom(src => src.Client.IndustryClassificationId))
                .ForMember(dest => dest.NcrcpNumber, opts => opts.MapFrom(src => src.Client.NcrcpNumber))
                .ForMember(dest => dest.NcrReportingPrimaryBusinessClassificationId, opts => opts.MapFrom(src => src.Client.NcrReportingPrimaryBusinessClassificationId))
                .ForMember(dest => dest.PrimaryBureauId, opts => opts.MapFrom(src => src.Client.PrimaryBureauId))
                .ForMember(dest => dest.PrincipleDebtRangeId, opts => opts.MapFrom(src => src.Client.PrincipleDebtRangeId))
                .ForMember(dest => dest.RegisteredName, opts => opts.MapFrom(src => src.Client.RegisteredName))
                .ForMember(dest => dest.RegisteredNumber, opts => opts.MapFrom(src => src.Client.RegisteredNumber))
                .ForMember(dest => dest.RejectReason, opts => opts.MapFrom(src => src.Client.DisqualificationReason))
                .ForMember(dest => dest.SecondaryBureauId, opts => opts.MapFrom(src => src.Client.SecondaryBureauId))
                .ForMember(dest => dest.StakeholderManager, opts => opts.MapFrom(src => src.Client.StakeholderManager))
                .ForMember(dest => dest.VatNumber, opts => opts.MapFrom(src => src.Client.VatNumber))
                .ForMember(dest => dest.Website, opts => opts.MapFrom(src => src.Client.Website))
                .ForMember(dest => dest.ShmName, opts => opts.MapFrom(src => src.Client.ShmName))
                .ForMember(dest => dest.ApplicationStatus, opts => opts.MapFrom(src => src.Client.ApplicationStatus))
                .ForMember(dest => dest.MemberType, opts => opts.MapFrom(src => src.Client.MemberType))
                .ForMember(dest => dest.IDDocument, opts => opts.MapFrom(src => src.Client.IDDocument))
                .ForMember(dest => dest.NcrCertificate, opts => opts.MapFrom(src => src.Client.NcrCertificate))
                .ForMember(dest => dest.TotalSRNs, opts => opts.MapFrom(src => src.Client.TotalSRNs))
                .ForMember(dest => dest.TotalActiveSRNs, opts => opts.MapFrom(src => src.Client.TotalActiveSRNs));

            CreateMap<MemberUsers, IdValuePairResource>()
                .ForPath(dest => dest.Id, opts => opts.MapFrom(src => (src.User == null) ? 0 : src.User.Id))
                .ForPath(dest => dest.Value, opts => opts.MapFrom(src => (src.User == null) ? "" : src.User.FirstName + " " + src.User.LastName));
            CreateMap<MemberUsers, UserGetResource>()
                .ForPath(dest => dest.Id, opts => opts.MapFrom(src => (src.User == null) ? 0 : src.User.Id))
                .ForPath(dest => dest.Email, opts => opts.MapFrom(src => src.User != null ? src.User.Email : ""))
                .ForPath(dest => dest.FirstName, opts => opts.MapFrom(src => src.User != null ? src.User.FirstName : ""))
                .ForPath(dest => dest.LastName, opts => opts.MapFrom(src => src.User != null ? src.User.LastName : ""))
                .ForPath(dest => dest.RoleId, opts => opts.MapFrom(src => src.User != null ? src.User.RoleId : 0))
                .ForPath(dest => dest.IsEmailConfirmed, opts => opts.MapFrom(src => src.User != null ? src.User.IsEmailConfirmed : false));

            CreateMap<ALGClientGetResource, MemberGetSimpleResource>()
                .ForMember(dest => dest.ApplicationStatusId, opts => opts.MapFrom(src => src.Client.ApplicationStatusId))
                .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Client.Id))
                .ForMember(dest => dest.IdNumber, opts => opts.MapFrom(src => src.Client.IdNumber))
                .ForMember(dest => dest.MembershipTypeId, opts => opts.MapFrom(src => src.Client.MembershipTypeId))
                .ForMember(dest => dest.RegisteredName, opts => opts.MapFrom(src => src.Client.RegisteredName))
                .ForMember(dest => dest.RegisteredNumber, opts => opts.MapFrom(src => src.Client.RegisteredNumber))
                .ForMember(dest => dest.StakeholderManager, opts => opts.MapFrom(src => src.Client.ShmName))
                .ForMember(dest => dest.ApplicationStatus, opts => opts.MapFrom(src => src.Client.ApplicationStatus))
                .ForMember(dest => dest.MembershipType, opts => opts.MapFrom(src => src.Client.MemberType))
                .ForMember(dest => dest.TotalSRNs, opts => opts.MapFrom(src => src.Client.TotalSRNs))
                .ForMember(dest => dest.TotalActiveSRNs, opts => opts.MapFrom(src => src.Client.TotalActiveSRNs))
                .ForMember(dest => dest.DateActivated, opts => opts.MapFrom(src => (src.Client.DateActivated != null) ? string.Format("{0:dd-MM-yyyy}", src.Client.DateActivated) : ""));

            CreateMap<IdValuePairResource, SRNStatusReason>();

            CreateMap<SRNStatusReason, IdValuePairResource>();
            CreateMap<SRNStatusReason, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Name));

            CreateMap<SRNStatusReasonSRNStatusLink, SRNStatusReasonCustomGetResource>()
                .ForMember(dest => dest.Status, opts => opts.MapFrom(src => src.SRNStatus));
            CreateMap<SRNStatus, SRNStatusReasonCustomGetResource>()
                .ForMember(dest => dest.Reasons, opts => opts.MapFrom(src => src.SRNStatusReasons))
                .ForMember(dest => dest.Status, opts => opts.MapFrom(src => src));

            CreateMap<SRNStatusReason, SRNStatusReasonGetResource>();

            CreateMap<SRNStatusReasonSRNStatusLink, IdValuePairResource>()
                .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.SRNStatusReasonId))
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.SRNStatusReason.Name));

            CreateMap<SRNStatusUpdateRequestResource, SRNStatusUpdateHistory>();
            CreateMap<SRNStatusUpdateHistory, SRNStatusUpdateHistoryGetResource>();

            CreateMap<SRNCreateResource, SRNStatusUpdateHistory>();
            CreateMap<SRNStatusUpdateHistory, SRNGetResource>();
            CreateMap<SRNStatusUpdateHistory, SRNGetResource>();

            CreateMap<SRNStatusUpdateHistory, SRNRolloutStatusResource>()
                .ForMember(dest => dest.RegisteredName, opts => opts.MapFrom(src => src.SRN.Member.RegisteredName))
                .ForMember(dest => dest.RegisteredNumber, opts => opts.MapFrom(src => src.SRN.Member.RegisteredNumber))
                .ForMember(dest => dest.SRNNumber, opts => opts.MapFrom(src => src.SRN.SRNNumber))
                .ForMember(dest => dest.TradingName, opts => opts.MapFrom(src => src.SRN.TradingName));

            CreateMap<ALGClientLeader, IdValuePairResource>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Leader.RegisteredName))
                .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.LeaderId));
            CreateMap<DWException, DWExceptionGetResource>();
            CreateMap<DWExceptionCreateResource, DWException>();
            CreateMap<DWExceptionUpdateResource, DWException>();
            CreateMap<DWExceptionExternalGetResource, DWExceptionCreateResource>();
            CreateMap<DWExceptionGetResource, DWExceptionTaskItemResource>();

            CreateMap<SRNStatusFileTypes, IdValuePairResource>();
            CreateMap<SRNStatusTypes, IdValuePairResource>();
            CreateMap<ALGClientLeader, ALGClientGetResource>();

            CreateMap<MemberContact, ContactOutputDTO>()
            .ForMember(dest => dest.CellphoneNumber, opts => opts.MapFrom(src => src.CellNumber))
            .ForMember(dest => dest.OfficeNumber, opts => opts.MapFrom(src => src.OfficeTelNumber))
            .ForMember(dest => dest.EmailAddress, opts => opts.MapFrom(src => src.Email))
            .ForMember(dest => dest.LastName, opts => opts.MapFrom(src => src.Surname))
            .ForMember(dest => dest.Designation, opts => opts.MapFrom(src => src.JobTitle));

            CreateMap<TradingName, TradingNameOutputDTO>();

            CreateMap<Member, MemberOutputDTO>()
                .ForPath(dest => dest.CompanyRegisteredName, opts => opts.MapFrom(src => src.RegisteredName))
                .ForPath(dest => dest.IsSoleProprietor, opts => opts.MapFrom(src => src.IsSoleProp))
                .ForPath(dest => dest.CompanyRegistrationNumber, opts => opts.MapFrom(src => src.RegisteredNumber))
                .ForPath(dest => dest.IdentificationNumber, opts => opts.MapFrom(src => src.IdNumber ?? "N/A"))
                .ForPath(dest => dest.CompanyTradingNames, opts => opts.MapFrom(src => src.TradingNames))
                .ForPath(dest => dest.IsVatRegistered, opts => opts.MapFrom(src => src.IsVatRegistrant))
                .ForPath(dest => dest.VatNumber, opts => opts.MapFrom(src => src.VatNumber == "N/A" ? null : src.VatNumber))
                .ForPath(dest => dest.CompanyWebsite, opts => opts.MapFrom(src => src.Website ?? "N/A"))
                .ForPath(dest => dest.SacrraIndustryClassId, opts => opts.MapFrom(src => (src.IndustryClassificationId > 0) ? src.IndustryClassificationId : 0))
                .ForPath(dest => dest.MembershipTypeId, opts => opts.MapFrom(src => src.MembershipTypeId))
                .ForPath(dest => dest.PrincipleDebtRangeId, opts => opts.MapFrom(src => (src.PrincipleDebtRangeId > 0) ? src.PrincipleDebtRangeId : 0))
                .ForPath(dest => dest.NCRReportingPrimaryBusinessClassificationId, opts => opts.MapFrom(src => (src.NcrReportingPrimaryBusinessClassificationId > 0) ? src.NcrReportingPrimaryBusinessClassificationId : 0))
                .ForPath(dest => dest.PrimaryBureauId, opts => opts.MapFrom(src => (src.PrimaryBureauId > 0) ? src.PrimaryBureauId : 0))
                .ForPath(dest => dest.SecondaryBureauId, opts => opts.MapFrom(src => (src.SecondaryBureauId > 0) ? src.SecondaryBureauId : 0))
                .ForPath(dest => dest.MiscComments, opts => opts.MapFrom(src => src.Comments ?? "N/A"))
                .ForPath(dest => dest.ApplicationStatus.Id, opts => opts.MapFrom(src => src.ApplicationStatusId))
                .ForMember(dest => dest.ChangeRequestStatus, opts => opts.MapFrom(src => src.ChangeRequest.Status))
                .ForMember(dest => dest.NcrcpNumber, opts => opts.MapFrom(src => src.NcrcpNumber))
                .ForMember(dest => dest.StatusReason, opts => opts.MapFrom(src => (src.MemberStatusReason != null) ? src.MemberStatusReason.Name: ""))
                .ForPath(dest => dest.StakeholderManager.Id, opts => opts.MapFrom(src => (src.StakeholderManager == null) ? 0 : src.StakeholderManager.Id))
                .ForPath(dest => dest.StakeholderManager.Value, opts => opts.MapFrom(src => (src.StakeholderManager == null) ? "N/A" : src.StakeholderManager.FirstName + " " + src.StakeholderManager.LastName))
                .ForPath(dest => dest.DisqualificationReason, opts => opts.MapFrom(src => src.DisqualificationReason ?? "N/A"))
                .ForPath(dest => dest.HeadOfficePostalAddress, opts => opts.MapFrom(src => src.HeadOfficePostalAddress ?? "N/A"))
                .ForPath(dest => dest.HeadOfficePhysicalAddress, opts => opts.MapFrom(src => src.HeadOfficePhysicalAddress ?? "N/A"))
                .ForPath(dest => dest.AnalyticsCompanyName, opts => opts.MapFrom(src => src.AnalyticsCompanyName ?? "N/A"))
                .ForMember(dest => dest.NCRCertificateDocument, opts => opts.MapFrom(src => (src.MemberDocument != null) ? JsonConvert.DeserializeObject<DocumentInputOutputDTO>(src.MemberDocument.NcrCertificateBlob) : null))
                .ForMember(dest => dest.IdentificationDocument, opts => opts.MapFrom(src => (src.MemberDocument != null) ? JsonConvert.DeserializeObject<DocumentInputOutputDTO>(src.MemberDocument.IDDocumentBlob) : null))
                .ForMember(dest => dest.AuditedFinancialDocument, opts => opts.MapFrom(src => (src.MemberDocument != null) ? JsonConvert.DeserializeObject<DocumentInputOutputDTO>(src.MemberDocument.AuditedFinancialBlob) : null))
                .ForMember(dest => dest.NCRFeeCategoryId, opts => opts.MapFrom(src => (!string.IsNullOrEmpty(src.NcrCategory) ? EnumHelper.GetEnumIdValuePair<NCRCategoryEnum>((int)Enum.Parse<NCRCategoryEnum>(src.NcrCategory)).Id : 0)));

            CreateMap<User, UserCamundaGetResource>();

            CreateMap<ALGClientLeader, ALGLeaderIdValuePairOutputDTO>()
                .ForMember(dest => dest.Value, opts => opts.MapFrom(src => src.Leader.RegisteredName))
                .ForMember(dest => dest.Id, opts => opts.MapFrom(src => src.Leader.Id));

            CreateMap<SRN, SRNUpdateResource>();
            #endregion
        }

        private List<SRNStatusUpdateHistory> CreateSRNStatusUpdatesV2(SRNRequestInputDTO src)
        {
            if (src != null)
            {
                List<SRNStatusUpdateHistory> updates = new List<SRNStatusUpdateHistory>();
                var updateNumber = Guid.NewGuid().ToString();

                if (src.FileType == SRNStatusFileTypes.MonthlyAndDailyFile)
                {
                    updates = new List<SRNStatusUpdateHistory>
                    {
                        new SRNStatusUpdateHistory
                            {
                                DailyFileDevelopmentStartDate = src.DailyFileDevelopmentStartDate,
                                DailyFileDevelopmentEndDate = src.DailyFileDevelopmentEndDate,
                                DailyFileTestStartDate = src.DailyFileTestStartDate,
                                DailyFileTestEndDate = src.DailyFileTestEndDate,
                                DailyFileGoLiveDate = src.DailyFileGoLiveDate,

                                MonthlyFileDevelopmentStartDate = src.MonthlyFileDevelopmentStartDate,
                                MonthlyFileDevelopmentEndDate = src.MonthlyFileDevelopmentEndDate,
                                MonthlyFileTestStartDate = src.MonthlyFileTestStartDate,
                                MonthlyFileTestEndDate = src.MonthlyFileTestEndDate,
                                MonthlyFileGoLiveDate = src.MonthlyFileGoLiveDate,
                                FileType = SRNStatusFileTypes.DailyFile,
                                DateCreated = DateTime.Now,
                                UpdateNumber = updateNumber
                            },

                        new SRNStatusUpdateHistory
                            {
                                DailyFileDevelopmentStartDate = src.DailyFileDevelopmentStartDate,
                                DailyFileDevelopmentEndDate = src.DailyFileDevelopmentEndDate,
                                DailyFileTestStartDate = src.DailyFileTestStartDate,
                                DailyFileTestEndDate = src.DailyFileTestEndDate,
                                DailyFileGoLiveDate = src.DailyFileGoLiveDate,

                                MonthlyFileDevelopmentStartDate = src.MonthlyFileDevelopmentStartDate,
                                MonthlyFileDevelopmentEndDate = src.MonthlyFileDevelopmentEndDate,
                                MonthlyFileTestStartDate = src.MonthlyFileTestStartDate,
                                MonthlyFileTestEndDate = src.MonthlyFileTestEndDate,
                                MonthlyFileGoLiveDate = src.MonthlyFileGoLiveDate,
                                FileType = SRNStatusFileTypes.MonthlyFile,
                                DateCreated = DateTime.Now,
                                UpdateNumber = updateNumber
                            }
                    };

                    return updates;
                }
                else
                {
                    updates = new List<SRNStatusUpdateHistory>
                    {
                        new SRNStatusUpdateHistory
                            {
                                DailyFileDevelopmentStartDate = src.DailyFileDevelopmentStartDate,
                                DailyFileDevelopmentEndDate = src.DailyFileDevelopmentEndDate,
                                DailyFileTestStartDate = src.DailyFileTestStartDate,
                                DailyFileTestEndDate = src.DailyFileTestEndDate,
                                DailyFileGoLiveDate = src.DailyFileGoLiveDate,

                                MonthlyFileDevelopmentStartDate = src.MonthlyFileDevelopmentStartDate,
                                MonthlyFileDevelopmentEndDate = src.MonthlyFileDevelopmentEndDate,
                                MonthlyFileTestStartDate = src.MonthlyFileTestStartDate,
                                MonthlyFileTestEndDate = src.MonthlyFileTestEndDate,
                                MonthlyFileGoLiveDate = src.MonthlyFileGoLiveDate,
                                FileType = src.FileType,
                                DateCreated = DateTime.Now,
                                UpdateNumber = updateNumber
                            }
                    };

                    return updates;
                }
            }

            return new List<SRNStatusUpdateHistory>();

        }

        private List<SRNStatusUpdateHistory> CreateSRNStatusUpdates(SRNCreateResource src)
        {
            if (src != null)
            {
                List<SRNStatusUpdateHistory> updates = new List<SRNStatusUpdateHistory>();
                var updateNumber = Guid.NewGuid().ToString();

                if (src.FileType == SRNStatusFileTypes.MonthlyAndDailyFile)
                {
                    updates = new List<SRNStatusUpdateHistory>
                    {
                        new SRNStatusUpdateHistory
                            {
                                DailyFileDevelopmentStartDate = src.DailyFileDevelopmentStartDate,
                                DailyFileDevelopmentEndDate = src.DailyFileDevelopmentEndDate,
                                DailyFileTestStartDate = (src.DailyFileTestStartDate != null)? src.DailyFileTestStartDate : Convert.ToDateTime("1900-01-01"),
                                DailyFileTestEndDate = src.DailyFileTestEndDate,
                                DailyFileGoLiveDate = (src.DailyFileGoLiveDate != null)? src.DailyFileGoLiveDate : Convert.ToDateTime("1900-01-01"),

                                MonthlyFileDevelopmentStartDate = src.MonthlyFileDevelopmentStartDate,
                                MonthlyFileDevelopmentEndDate = src.MonthlyFileDevelopmentEndDate,
                                MonthlyFileTestStartDate = (src.MonthlyFileTestStartDate != null)? src.MonthlyFileTestStartDate : Convert.ToDateTime("1900-01-01"),
                                MonthlyFileTestEndDate = src.MonthlyFileTestEndDate,
                                MonthlyFileGoLiveDate = (src.MonthlyFileGoLiveDate != null)? src.MonthlyFileGoLiveDate : Convert.ToDateTime("1900-01-01"),
                                FileType = SRNStatusFileTypes.DailyFile,
                                DateCreated = DateTime.Now,
                                UpdateNumber = updateNumber
                            },

                        new SRNStatusUpdateHistory
                            {
                                DailyFileDevelopmentStartDate = src.DailyFileDevelopmentStartDate,
                                DailyFileDevelopmentEndDate = src.DailyFileDevelopmentEndDate,
                                DailyFileTestStartDate = (src.DailyFileTestStartDate != null)? src.DailyFileTestStartDate : Convert.ToDateTime("1900-01-01"),
                                DailyFileTestEndDate = src.DailyFileTestEndDate,
                                DailyFileGoLiveDate = (src.DailyFileGoLiveDate != null)? src.DailyFileGoLiveDate : Convert.ToDateTime("1900-01-01"),

                                MonthlyFileDevelopmentStartDate = src.MonthlyFileDevelopmentStartDate,
                                MonthlyFileDevelopmentEndDate = src.MonthlyFileDevelopmentEndDate,
                                MonthlyFileTestStartDate = (src.MonthlyFileTestStartDate != null)? src.MonthlyFileTestStartDate : Convert.ToDateTime("1900-01-01"),
                                MonthlyFileTestEndDate = src.MonthlyFileTestEndDate,
                                MonthlyFileGoLiveDate = (src.MonthlyFileGoLiveDate != null)? src.MonthlyFileGoLiveDate : Convert.ToDateTime("1900-01-01"),
                                FileType = SRNStatusFileTypes.MonthlyFile,
                                DateCreated = DateTime.Now,
                                UpdateNumber = updateNumber
                            }
                    };

                    return updates;
                }
                else
                {
                    updates = new List<SRNStatusUpdateHistory>
                    {
                        new SRNStatusUpdateHistory
                            {
                                DailyFileDevelopmentStartDate = src.DailyFileDevelopmentStartDate,
                                DailyFileDevelopmentEndDate = src.DailyFileDevelopmentEndDate,
                                DailyFileTestStartDate = (src.DailyFileTestStartDate == null && src.FileType == SRNStatusFileTypes.DailyFile)? Convert.ToDateTime("1900-01-01") : src.DailyFileTestStartDate,
                                DailyFileTestEndDate = src.DailyFileTestEndDate,
                                DailyFileGoLiveDate = (src.DailyFileGoLiveDate == null && src.FileType == SRNStatusFileTypes.DailyFile)? Convert.ToDateTime("1900-01-01") : src.DailyFileGoLiveDate,

                                MonthlyFileDevelopmentStartDate = src.MonthlyFileDevelopmentStartDate,
                                MonthlyFileDevelopmentEndDate = src.MonthlyFileDevelopmentEndDate,
                                MonthlyFileTestStartDate = (src.MonthlyFileTestStartDate == null && src.FileType == SRNStatusFileTypes.MonthlyFile)? Convert.ToDateTime("1900-01-01") : src.MonthlyFileTestStartDate,
                                MonthlyFileTestEndDate = src.MonthlyFileTestEndDate,
                                MonthlyFileGoLiveDate = (src.MonthlyFileGoLiveDate == null && src.FileType == SRNStatusFileTypes.MonthlyFile)? Convert.ToDateTime("1900-01-01") : src.MonthlyFileGoLiveDate,
                                FileType = src.FileType,
                                DateCreated = DateTime.Now,
                                UpdateNumber = updateNumber
                            }
                    };

                    return updates;
                }
            }

            return new List<SRNStatusUpdateHistory>();

        }

        private string PopulateSRNTestStartDate(SRNRolloutStatusModel src)
        {
            string testStartDate;

            if (src != null)
            {
                if (src.FileType != null)
                {
                    if (src.FileType == "Monthly")
                    {
                        testStartDate = (src.MonthlyFileTestStartDate != null) ? src.MonthlyFileTestStartDate.ToString() : null;
                        return testStartDate;
                    }
                    else if (src.FileType == "Daily")
                    {
                        testStartDate = (src.DailyFileTestStartDate != null) ? src.DailyFileTestStartDate.ToString() : null;
                        return testStartDate;
                    }
                }
            }

            return null;
        }

        private string PopulateSRNTestEndDate(SRNRolloutStatusModel src)
        {
            string testEndDate;

            if (src != null)
            {
                if (src.FileType != null)
                {
                    if (src.FileType == "Monthly")
                    {
                        testEndDate = (src.MonthlyFileTestEndDate != null) ? src.MonthlyFileTestEndDate.ToString() : null;
                        return testEndDate;
                    }
                    else if (src.FileType == "Daily")
                    {
                        testEndDate = (src.DailyFileTestEndDate != null) ? src.DailyFileTestEndDate.ToString() : null;
                        return testEndDate;
                    }
                }
            }

            return null;
        }

        private string PopulateSRNGoLiveDate(SRNRolloutStatusModel src)
        {
            string goLiveDate;

            if (src != null)
            {
                if (src.FileType != null)
                {
                    if (src.FileType == "Monthly")
                    {
                        goLiveDate = (src.MonthlyFileGoLiveDate != null) ? src.MonthlyFileGoLiveDate.ToString() : null;
                        return goLiveDate;
                    }
                    else if (src.FileType == "Daily")
                    {
                        goLiveDate = (src.DailyFileGoLiveDate != null) ? src.DailyFileGoLiveDate.ToString() : null;
                        return goLiveDate;
                    }
                }
            }

            return null;
        }

        private SRNStatusFileTypes CheckFileType(SRNRequestInputDTO src)
        {
            if (src.IsDailyFile == true && src.IsMonthlyFile == true)
            {
                return SRNStatusFileTypes.MonthlyAndDailyFile;
            }
            else if (src.IsDailyFile == true)
            {
                return SRNStatusFileTypes.DailyFile;
            }
            else
            {
                return SRNStatusFileTypes.MonthlyFile;
            }
        }

        private List<string> PopulateALGLeaderClients(ICollection<ALGClientLeader> clients)
        {
            if(clients != null)
            {
                List<string> clientNames = new List<string>();
                foreach (ALGClientLeader client in clients)
                {
                    if(client.Client != null)
                        clientNames.Add(client.Client.RegisteredName);
                }

                return clientNames;
            }
            return new List<string>();
        }
    }
}
