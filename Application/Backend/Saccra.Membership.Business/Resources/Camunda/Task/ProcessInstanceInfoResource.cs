using Camunda.Api.Client;
using System.Collections.Generic;

namespace Sacrra.Membership.Business.Resources.Camunda.Task
{
    public class ProcessInstanceInfoResource
    {
        //
        // Summary:
        //     The id of the process instance.
        public string Id;
        //
        // Summary:
        //     The id of the process definition this instance belongs to.
        public string DefinitionId;
        //
        // Summary:
        //     The business key of the process instance.
        public string BusinessKey;
        //
        // Summary:
        //     The id of the case instance associated with the process instance.
        public string CaseInstanceId;
        //
        // Summary:
        //     A flag indicating whether the process instance has ended or not. Deprecated:
        //     will always be false!
        public bool Ended;
        //
        // Summary:
        //     A flag indicating whether the process instance is suspended or not.
        public bool Suspended;
        //
        // Summary:
        //     The tenant id of the process instance.
        public string TenantId;

        //
        // Summary:
        //     Object containing a property for each of the latest variables.
        public Dictionary<string, VariableValue> Variables;
    }
}
