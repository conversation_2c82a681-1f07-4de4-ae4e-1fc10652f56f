using System;
using System.Collections.Generic;

namespace Sacrra.Membership.Business.Resources.Camunda.Task
{
    // What I deplay on the front-end
    public class TaskSHMSrnMergeReviewGetResource
    {
        public DateTime MergeDate { get; set; }

        public DateTime FileDevStartDate { get; set; } // TODO: Verify this

        public DateTime FileDevEndDate { get; set; } // TODO: Verify this

        public DateTime FileTestStartDate { get; set; }

        public DateTime FileTestEndDate { get; set; }

        public DateTime GoLiveDate { get; set; }

        public int MergeToSRNId { get; set; }

        public List<int> SRNIdMergeFromList { get; set; }
    }
}
