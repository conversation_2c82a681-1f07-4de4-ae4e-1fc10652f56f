using Sacrra.Membership.Business.Resources.BranchLocation;
using Sacrra.Membership.Business.Resources.SRNContact;
using Sacrra.Membership.Database.Enums;
using System.Collections.Generic;

namespace Sacrra.Membership.Business.Resources.SRN
{
    public class SRNCreateResource : SRNBaseResource
    {
        public int? LoanManagementSystemVendorId { get; set; }
        public int? SoftwareVendorId { get; set; }
        public int MemberId { get; set; }
        public int AccountTypeId { get; set; }
        public int? NCRReportingAccountTypeClassificationId { get; set; }
        public int BillingCycleDay { get; set; }
        public List<SRNContactCreateResource> Contacts { get; set; }
        public int? SPGroupId { get; set; }
        public List<BranchLocationSRNCreateResource> BranchLocations { get; set; }
        public int? ALGLeaderId { get; set; }
        public SRNStatusFileTypes FileType { get; set; }
    }
}
