using System;

namespace Sacrra.Membership.Business.Resources.SRN
{
    public class SRNSummaryExportResource
    {
        public string DataContributor { get; set; }
        public string CompanyRegNumber { get; set; }
        public string SACRRAIndustryClassification { get; set; }
        public string StakeholderManager { get; set; }
        public string ALGLeader { get; set; }
        public string SPNumber { get; set; }
        public string SRNNumber { get; set; }
        public string SRNTradingName { get; set; }
        public string SRNAccountType { get; set; }
        public string SRNReportingAccountTypeClassification { get; set; }
        public string SRNStatus { get; set; }
        public DateTime BureauClosureDate { get; set; }
        public string DataContactFirstName { get; set; }
        public string DataContactSurname { get; set; }
        public string DataContactOfficeNumber { get; set; }
        public string DataContactCellNumber { get; set; }
        public string DataContactEmail { get; set; }
        public DateTime? StatusLastUpdatedAt { get; set; }
    }
}
