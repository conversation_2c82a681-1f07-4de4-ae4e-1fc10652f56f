using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using RestSharp;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Resources.Camunda.Task;
using Sacrra.Membership.Business.Services.DataWarehouseService;
using Sacrra.Membership.Database;
using System;

namespace Sacrra.Membership.Business.Services.HangfireJobsService
{
    public class HangfireJobsService
    {
        private DataWarehouseService.DataWarehouseService _dataWarehouseService;
        private readonly ConfigSettings _configSettings;
        private readonly AppDbContext _dbContext;
        private readonly GlobalHelper _globalHelper;

        public HangfireJobsService(GlobalHelper globalHelper, AppDbContext dbContext, IOptions<ConfigSettings> configSettings, IConfiguration configuration)
        {
            _globalHelper = globalHelper;
            _dbContext = dbContext;
            _configSettings = configSettings.Value;
        }

        public void RecheckDthSubmission()
        {
            // Get all tasks that has a submission that is due
            try
            {
                var getClient = new RestClient();
                var getRestRequest = new RestRequest(_configSettings.CamundaBaseAddress + "/task?taskDefinitionKey=Task_1nyudk8");
                var response = getClient.Get(getRestRequest);
                var taskList = JsonConvert.DeserializeObject<CamundaTaskDTO[]>(response.Content);

                foreach (var task in taskList)
                {
                    // Complete Task
                    var client = new RestClient();
                    RestRequest restRequest;
                    var taskVariables = new
                    {
                        variables = new
                        {
                            recheckDthSubmission = new { value = "true", type = "boolean" }
                        }
                    };

                    restRequest = new RestRequest(_configSettings.CamundaBaseAddress + "/task/" + task.Id + "/complete")
                    .AddJsonBody(JsonConvert.SerializeObject(taskVariables));
                    client.Post(restRequest);
                }
            } catch (Exception exception)
            {
                _globalHelper.LogError(_dbContext, exception, $"Unable to complete task to check if replacement file was submitted to DTH");
                throw new Exception($"Unable to complete task to check if replacement file was submitted to DTH");
            }
        }
    }
}
