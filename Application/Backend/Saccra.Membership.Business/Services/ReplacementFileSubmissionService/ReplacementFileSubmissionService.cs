using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using RestSharp;
using Sacrra.Membership.Business.DTOs.DataWarehouseDTO;
using Sacrra.Membership.Business.Exceptions;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Services.DataWarehouseService;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Notification.Repositories;
using Sacrra.Membership.Reporting.Helper;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using Sacrra.Membership.Business.DTOs.ReplacementFileSubmissionDTOs;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;

namespace Sacrra.Membership.Business.Services.ReplacementFileSubmissionService
{
    public class ReplacementFileSubmissionService
    {
        private AppDbContext _dbContext;
        private readonly ConfigSettings _configSettings;
        private readonly EmailService _emailService;
        private DataWarehouseService.DataWarehouseService _dataWarehouseService;
        private ReportTables _reportTables;

        public ReplacementFileSubmissionService(AppDbContext dbContext, IOptions<ConfigSettings> configSettings, EmailService emailService, DataWarehouseService.DataWarehouseService dataWarehouseService)
        {
            _dbContext = dbContext;
            _configSettings = configSettings.Value;
            _emailService = emailService;
            _dataWarehouseService = dataWarehouseService;
            _reportTables = _dataWarehouseService.ReportTables;
        }

        public async Task<RestResponse> RequestReplacementFileSubmission(ReplacementFileSubmissionInputDTO inputDTO)
        {
            SRN replacementFileSRN;
            SPGroup replacementFileSpNumber;
            Member replacementFileMember;
            var restClient = new RestClient();
            object variables;
            ReplacementFileSubmission replacementFileSubmission;
            var user = await Helpers.Helpers.GetUserByAuth0Id(_dbContext);

            replacementFileSpNumber = _dbContext.SPGroups.Where(x => x.SPNumber == inputDTO.SPNumber).FirstOrDefault();
            replacementFileSRN = _dbContext.SRNs.Where(x => x.SRNNumber == inputDTO.SRNNumber).FirstOrDefault();

            if (replacementFileSRN != null && replacementFileSpNumber == null)
            {
                replacementFileMember = _dbContext.Members.Where(x => x.Id == replacementFileSRN.MemberId)
                    .Include(x => x.StakeholderManager)
                    .FirstOrDefault();
            }
            else if (replacementFileSpNumber != null)
            {
                replacementFileMember = _dbContext.Members.Where(x => x.Id == replacementFileSpNumber.MemberId)
                    .Include(x => x.StakeholderManager)
                    .FirstOrDefault();
            }
            else
            {
                throw new Exception($"SRN ({inputDTO.SRNNumber}) was not found.");
            }

            replacementFileSubmission = new ReplacementFileSubmission
            {
                SRNId = replacementFileSRN != null && replacementFileSpNumber == null ? replacementFileSRN.Id : null,
                SPId = replacementFileSpNumber != null ? replacementFileSpNumber.Id : null,
                MemberId = replacementFileMember.Id,
                FileName = inputDTO.FileName,
                ReplacementFileName = inputDTO.ReplacementFileName,
                NumberOfRecords = inputDTO.NumberOfRecords,
                NumberOfFiles = inputDTO.NumberOfFiles,
                ReplacementFileSubmissionStatusId = (int)ReplacementFileSubmissionStatuses.Requested,
                FileSubmissionTypeId = 1,
                SubmissionStatusDate = DateTime.Now,
                ReplacementFileSubmissionReasonId = inputDTO.ReasonForReplacementFileId,
                SACRRAIndustry = EnumHelper.GetEnumIdValuePair<IndustryClassifications>((int)replacementFileMember.IndustryClassificationId)?.Value,
                SACRRAAccountType = _dbContext.AccountTypes.Where(x => x.Id == replacementFileSRN.AccountTypeId).FirstOrDefault()?.Name,
                IsDeleted = false,
                CreatedAt = DateTime.Now,
                LastUpdatedAt = DateTime.Now,
                PlannedSubmissionDate = DateTime.Parse(inputDTO.PlannedSubmissionDate),
                ActualSubmissionDate = null
            };

            try
            {
                _dbContext.ReplacementFileSubmissions.Add(replacementFileSubmission);

                //Build eventlog var
                var updateDetailsBlob = JsonConvert.SerializeObject(replacementFileSubmission);
                var stagingChangeLog = new StagingChange
                {
                    Name = "Adhoc Replacement",
                    OldValue = "",
                    NewValue = inputDTO.FileName
                };
                var stagingDetailsBlob = JsonConvert.SerializeObject(stagingChangeLog);
                //Update EventLog
                await Helpers.Helpers
                        .CreateEventLog(_dbContext, user.Id, "Adhoc Replacement", inputDTO.FileName, updateDetailsBlob, stagingDetailsBlob, replacementFileMember.Id, "Adhoc");
            }
            catch (Exception exception)
            {
                throw new Exception("Unable to save replacement file request to DB.", exception);
            }

            if (replacementFileSRN != null && replacementFileSpNumber == null)
            {
                variables = new
                {
                    variables = new
                    {
                        SRNId = new
                        {
                            value = replacementFileSRN.Id,
                            type = "long"
                        },
                        SHMId = new
                        {
                            value = replacementFileMember.StakeholderManagerId,
                            type = "long"
                        },
                        FileSubmissionRequestId = new
                        {
                            value = replacementFileSubmission.Id,
                            type = "long"
                        },
                        plannedSubmissionDate = new
                        {
                            value = DateTime.Parse(inputDTO.PlannedSubmissionDate).AddDays(1).ToString("yyyy-MM-dd"),
                            type = "string"
                        }
                    }
                };
            }
            else
            {
                variables = new
                {
                    variables = new
                    {
                        SPId = new
                        {
                            value = replacementFileSpNumber.Id,
                            type = "long"
                        },
                        SHMId = new
                        {
                            value = replacementFileMember.StakeholderManagerId,
                            type = "long"
                        },
                        FileSubmissionRequestId = new
                        {
                            value = replacementFileSubmission.Id,
                            type = "long"
                        },
                        plannedSubmissionDate = new
                        {
                            value = DateTime.Parse(inputDTO.PlannedSubmissionDate).AddDays(1).ToString("yyyy-MM-dd"),
                            type = "string"
                        }
                    }
                };
            }

            try
            {
                return restClient.Post(new RestRequest(_configSettings.CamundaBaseAddress + "/process-definition/key/Replacement-File-Submissions/start")
                    .AddJsonBody(JsonConvert.SerializeObject(variables)));
            }
            catch (Exception exception)
            {
                replacementFileSubmission.IsDeleted = true;
                replacementFileSubmission.ReasonForDeletion = "Camunda task creation failed.";

                _dbContext.ReplacementFileSubmissions.Update(replacementFileSubmission);
                await _dbContext.SaveChangesAsync();

                throw new Exception("Camunda task creation failed.", exception);
            }
        }

        public List<ReplacementFileSubmissionOutputDTO> GetApprovedReplacementFiles()
        {
            var approvedReplacementFileRequestList = new List<ReplacementFileSubmissionOutputDTO>();
            var approvedReplacementFileRequests = _dbContext.ReplacementFileSubmissions
                .Where(x => x.ReplacementFileSubmissionStatusId == (int)ReplacementFileSubmissionStatuses.Submitted)
                .Include(x => x.SRN)
                .Include(x => x.Member)
                .ThenInclude(x => x.StakeholderManager)
                .ToList();

            approvedReplacementFileRequests.ForEach(approvedReplacementFileRequest =>
            {
                var dto = new ReplacementFileSubmissionOutputDTO
                {
                    Id = approvedReplacementFileRequest.Id,
                    NumberOfFiles = approvedReplacementFileRequest.NumberOfFiles,
                    NumberOfRecords = approvedReplacementFileRequest.NumberOfRecords,
                    SRNNumber = approvedReplacementFileRequest.SRN == null ? "N/A" : approvedReplacementFileRequest.SRN.SRNNumber,
                    ReplacementFileName = approvedReplacementFileRequest.ReplacementFileName,
                    MemberName = approvedReplacementFileRequest.Member.RegisteredName,
                    FileSubmissionReason = _dbContext.ReplacementFileSubmissionReasons.Where(reason => reason.Id == approvedReplacementFileRequest.ReplacementFileSubmissionReasonId).FirstOrDefault().Name,
                    SRNDisplayName = approvedReplacementFileRequest.SRN == null ? "N/A" : approvedReplacementFileRequest.SRN.TradingName,
                    StakeHolderManager = approvedReplacementFileRequest.Member.StakeholderManager.FullName,
                    PlannedSubmissionDate = approvedReplacementFileRequest.PlannedSubmissionDate.ToString("yyyy-MM-dd"),
                    OriginalFileName = approvedReplacementFileRequest.FileName,
                    SPNumber = approvedReplacementFileRequest.SRN == null ? _dbContext.SPGroups.Where(x => x.Id == approvedReplacementFileRequest.SPId).FirstOrDefault().SPNumber : "N/A",
                    ActualSubmissionDate = approvedReplacementFileRequest.ActualSubmissionDate == null ? "N/A" : approvedReplacementFileRequest.ActualSubmissionDate?.ToString("yyyy-MM-dd")
                };

                approvedReplacementFileRequestList.Add(dto);
            });

            return approvedReplacementFileRequestList;
        }

        public async Task SubmitUnsuccessfulReplacementLoad(BureauUnsuccessfulLoadInputDTO inputDTO, ClaimsPrincipal user)
        {
            try
            {
                var auth0User = await Helpers.Helpers.GetLoggedOnUser(_dbContext, user);
                var currentUser = _dbContext.Users
                    .Where(x => x.Auth0Id == auth0User.Auth0Id)
                    .Include(x => x.Members)
                    .FirstOrDefault();
                var bureauUnsuccessfulLoadModel = _dbContext.ReplacementFileSchedule
                    .Where(x => x.ReplacementFileSubmissionId == inputDTO.AdhocFileSubmissionId)
                    .FirstOrDefault();
                var replacementFileSubmission = _dbContext.ReplacementFileSubmissions
                    .Where(x => x.ReplacementFileName == inputDTO.AdHocFileName)
                    .Include(x => x.SRN)
                    .FirstOrDefault();
                var spNumber = _dbContext.SPGroups
                    .Where(x => x.Id == replacementFileSubmission.SPId)
                    .FirstOrDefault();
                var srn = _dbContext.SRNs
                    .Where(x => x.Id == replacementFileSubmission.SRNId)
                    .Include(x => x.Contacts)
                    .FirstOrDefault();
                var member = srn == null ? _dbContext.Members
                    .Where(x => x.Id == spNumber.MemberId)
                    .Include(x => x.Contacts)
                    .FirstOrDefault() : _dbContext.Members
                    .Where(x => x.Id == srn.MemberId)
                    .Include(x => x.Contacts)
                    .FirstOrDefault();

                bureauUnsuccessfulLoadModel.UnnsuccessfulLoadReasonId = inputDTO.UnsuccessfullLoadReasonId;
                bureauUnsuccessfulLoadModel.ReplacementFileBureauStatusId = ReplacementFileBureauStatuses.BureauLoadUnsuccessful;

                _dbContext.ReplacementFileSchedule.Update(bureauUnsuccessfulLoadModel);
                await _dbContext.SaveChangesAsync();

                var placeholders = new List<KeyValuePair<string, string>>
                    {
                        new KeyValuePair<string, string>("[Bureau]", member.RegisteredName),
                        new KeyValuePair<string, string>("[FileName]", inputDTO.AdHocFileName)
                    };
                if (srn == null)
                {
                    var dataContact = member.Contacts.FirstOrDefault(x => x.ContactTypeId == 1);
                    _emailService.SendEmail(dataContact.Email, dataContact.FirstName, "Bureau Unsuccessful Load", "BureauUnsuccessfulLoad.html", placeholders, null, "", "", spNumber.Id, WorkflowEnum.NotApplicable, EmailReasonEnum.BureauUnsuccessfulLoad, EmailRecipientTypeEnum.Member);
                }

                if (spNumber == null)
                {
                    var dataContact = srn.Contacts.FirstOrDefault(x => x.ContactTypeId == 5);
                    _emailService.SendEmail(dataContact.Email, dataContact.FirstName, "Bureau Unsuccessful Load", "BureauUnsuccessfulLoad.html", placeholders, null, "", "", srn.Id, WorkflowEnum.NotApplicable, EmailReasonEnum.BureauUnsuccessfulLoad, EmailRecipientTypeEnum.Member);
                }
            }
            catch (Exception ex)
            {
                throw new Exception("Unable to create unsuccessful load.");
            }
        }

        public async Task<ActionResult> SetSuccessfulFileLoad(BureauSuccessfulLoadInputDTO data)
        {
            var bureauSuccessfulLoadModel = _dbContext.ReplacementFileSchedule
                    .Where(x => x.ReplacementFileSubmissionId == data.ReplacementFileSubmissionId)
                    .FirstOrDefault();

            bureauSuccessfulLoadModel.ReplacementFileBureauStatusId = ReplacementFileBureauStatuses.BureauLoadSuccessful;
            bureauSuccessfulLoadModel.UnnsuccessfulLoadReasonId = null;

            _dbContext.ReplacementFileSchedule.Update(bureauSuccessfulLoadModel);
            await _dbContext.SaveChangesAsync();

            return new OkResult();

        }

        public async Task<List<ReplacementFileScheduleOutputDTO>> GetReplacementFileSchedule(ClaimsPrincipal user)
        {
            try
            {
                var auth0User = await Helpers.Helpers.GetLoggedOnUser(_dbContext, user);
                var currentUser = _dbContext.Users.Where(x => x.Auth0Id == auth0User.Auth0Id)
                    .Include(x => x.Members)
                    .ThenInclude(x => x.Member.StakeholderManager)
                    .FirstOrDefault();
                var replacementFileScheduleList = new List<ReplacementFileSchedule>();

                //var dailyAndMonthlyFileSubmissionsPerBureauAndStatusWhereClause = $"1 = 1";
                //DataWarehouseAPIModel dailyAndMonthlyFileSubmissionsPerBureauAndStatusApiCallModel;

                switch (auth0User.RoleId)
                {
                    case UserRoles.StakeHolderManager:
                        replacementFileScheduleList = _dbContext.ReplacementFileSchedule
                            .Where(x => x.Id != null)
                            .Include(x => x.ReplacementFileSubmission)
                            .ThenInclude(x => x.SRN)
                            .ThenInclude(x => x.Member)
                            .ThenInclude(x => x.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.Member)
                            .Include(x => x.ReplacementFileSubmission.Member.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.ReplacementFileSubmissionReason)
                            .Include(x => x.Bureau)
                            .Include(x => x.UnnsuccessfulLoadReason)
                            .ToList();
                        break;

                    case UserRoles.StakeHolderAdministrator:
                        replacementFileScheduleList = _dbContext.ReplacementFileSchedule
                            .Where(x => x.Id != null)
                            .Include(x => x.ReplacementFileSubmission)
                            .ThenInclude(x => x.SRN)
                            .ThenInclude(x => x.Member)
                            .ThenInclude(x => x.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.Member)
                            .Include(x => x.ReplacementFileSubmission.Member.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.ReplacementFileSubmissionReason)
                            .Include(x => x.Bureau)
                            .Include(x => x.UnnsuccessfulLoadReason)
                            .ToList();
                        break;

                    case UserRoles.FinancialAdministrator:
                        replacementFileScheduleList = _dbContext.ReplacementFileSchedule
                            .Where(x => x.Id != null)
                            .Include(x => x.ReplacementFileSubmission)
                            .ThenInclude(x => x.SRN)
                            .ThenInclude(x => x.Member)
                            .ThenInclude(x => x.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.Member)
                            .Include(x => x.ReplacementFileSubmission.Member.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.ReplacementFileSubmissionReason)
                            .Include(x => x.Bureau)
                            .Include(x => x.UnnsuccessfulLoadReason)
                            .ToList();
                        break;

                    case UserRoles.Member:
                        replacementFileScheduleList = _dbContext.ReplacementFileSchedule
                            .Include(x => x.ReplacementFileSubmission)
                            .ThenInclude(x => x.SRN)
                            .ThenInclude(x => x.Member)
                            .ThenInclude(x => x.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.Member)
                            .Include(x => x.ReplacementFileSubmission.Member.StakeholderManager)
                            .Include(x => x.Bureau)
                            .Include(x => x.ReplacementFileSubmission.ReplacementFileSubmissionReason)
                            .Include(x => x.UnnsuccessfulLoadReason)
                            .Where(x => x.ReplacementFileSubmission.MemberId == currentUser.Members.FirstOrDefault().Member.Id)
                            .ToList();
                        break;

                    case UserRoles.SACRRAAdministrator:
                        replacementFileScheduleList = _dbContext.ReplacementFileSchedule
                            .Where(x => x.Id != null)
                            .Include(x => x.ReplacementFileSubmission)
                            .ThenInclude(x => x.SRN)
                            .ThenInclude(x => x.Member)
                            .ThenInclude(x => x.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.Member)
                            .Include(x => x.ReplacementFileSubmission.Member.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.ReplacementFileSubmissionReason)
                            .Include(x => x.Bureau)
                            .Include(x => x.UnnsuccessfulLoadReason)
                            .ToList();
                        break;

                    case UserRoles.Bureau:
                        replacementFileScheduleList = _dbContext.ReplacementFileSchedule
                            .Include(x => x.ReplacementFileSubmission)
                            .ThenInclude(x => x.SRN)
                            .ThenInclude(x => x.Member)
                            .ThenInclude(x => x.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.Member)
                            .Include(x => x.ReplacementFileSubmission.Member.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.ReplacementFileSubmissionReason)
                            .Include(x => x.Bureau)
                            .Include(x => x.UnnsuccessfulLoadReason)
                            .Where(x => x.BureauId == currentUser.Members.FirstOrDefault().Member.Id)
                            .ToList();
                        break;

                    case UserRoles.User:
                        throw new UnauthorizedException();

                    case UserRoles.ALGLeader:
                        var replacementFileScheduleALGLeaderList = _dbContext.ReplacementFileSchedule
                            .Where(x => x.Id != null)
                            .Include(x => x.ReplacementFileSubmission)
                            .ThenInclude(x => x.SRN)
                            .ThenInclude(x => x.Member)
                            .ThenInclude(x => x.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.Member)
                            .Include(x => x.ReplacementFileSubmission.Member.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.ReplacementFileSubmissionReason)
                            .Include(x => x.Bureau)
                            .Include(x => x.UnnsuccessfulLoadReason)
                            .ToList();

                        var currentUserMembers = currentUser.Members.ToList();

                        for (int i = 0; i < currentUserMembers.Count; i++)
                        {
                            var member = currentUserMembers[i];

                            for (int j = 0; j < replacementFileScheduleALGLeaderList.Count; j++)
                            {
                                var replacementFileScheduleItem = replacementFileScheduleALGLeaderList[j];

                                if (replacementFileScheduleItem.ReplacementFileSubmission.MemberId == member.MemberId)
                                {
                                    replacementFileScheduleList.Add(replacementFileScheduleItem);
                                }
                            }
                        }
                        break;

                    case UserRoles.SystemAdministrator:
                        replacementFileScheduleList = _dbContext.ReplacementFileSchedule
                            .Where(x => x.Id != null)
                            .Include(x => x.ReplacementFileSubmission)
                            .ThenInclude(x => x.SRN)
                            .ThenInclude(x => x.Member)
                            .ThenInclude(x => x.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.Member)
                            .Include(x => x.ReplacementFileSubmission.Member.StakeholderManager)
                            .Include(x => x.ReplacementFileSubmission.ReplacementFileSubmissionReason)
                            .Include(x => x.Bureau)
                            .Include(x => x.UnnsuccessfulLoadReason)
                            .ToList();
                        break;

                    default:
                        break;
                }

                if (replacementFileScheduleList.Count > 0)
                {
                    //var counter = 0;
                    //var previousFileName = "";

                    //dailyAndMonthlyFileSubmissionsPerBureauAndStatusWhereClause += $" AND IsLatest = 1 AND FileName IN (";

                    //foreach (var item in replacementFileScheduleList)
                    //{
                    //    if (previousFileName == "")
                    //    {
                    //        if (counter < (replacementFileScheduleList.Count - 1))
                    //        {
                    //            dailyAndMonthlyFileSubmissionsPerBureauAndStatusWhereClause += $"'{item.ReplacementFileName}'";
                    //        }
                    //    }

                    //    if (previousFileName != item.ReplacementFileName && previousFileName != "")
                    //    {
                    //        if (counter < (replacementFileScheduleList.Count - 1))
                    //        {
                    //            dailyAndMonthlyFileSubmissionsPerBureauAndStatusWhereClause += $", '{item.ReplacementFileName}'";
                    //        }
                    //    }

                    //    previousFileName = item.ReplacementFileName;
                    //    counter++;
                    //}

                    //dailyAndMonthlyFileSubmissionsPerBureauAndStatusWhereClause += $")";
                    //dailyAndMonthlyFileSubmissionsPerBureauAndStatusApiCallModel = new DataWarehouseAPIModel()
                    //{
                    //    Columns = "*",
                    //    Where = dailyAndMonthlyFileSubmissionsPerBureauAndStatusWhereClause
                    //};

                    var replacementFileScheduleOutputDTOList = new List<ReplacementFileScheduleOutputDTO>();
                    //var dwReplacementFile = _dataWarehouseService.GetResultArray<ReplacementFileBureauOutputDTO>(_reportTables.DailyAndMonthlyFileSubmissionsPerBureauAndStatus, dailyAndMonthlyFileSubmissionsPerBureauAndStatusApiCallModel)
                    //    .ToList();
                    //var replacementFileSubmissionReasons = _dbContext.ReplacementFileSubmissionReasons.ToList();
                    //var algClientLeaders = _dbContext.ALGClientLeaders.Include(x => x.Leader).ToList();

                    foreach (var replacementFileSchedule in replacementFileScheduleList)
                    {
                        //var dwReplacementFileForBureau = dwReplacementFile
                        //    .Where(x => x.FileName == replacementFileSchedule.ReplacementFileName)
                        //    .Where(x => x.BureauName.ToLower() == bureauName.ToLower())
                        //    .FirstOrDefault();

                        //if (dwReplacementFileForBureau != null && dwReplacementFileForBureau.IsFileLoaded)
                        //{
                        //    replacementFileSchedule.ReplacementFileBureauStatusId = ReplacementFileBureauStatuses.BureauLoadSuccessful;
                        //}
                        //else
                        //{
                        //    if (replacementFileSchedule.ReplacementFileBureauStatusId != ReplacementFileBureauStatuses.BureauLoadPending
                        //        && replacementFileSchedule.ReplacementFileBureauStatusId != ReplacementFileBureauStatuses.BureauLoadUnsuccessful
                        //        && replacementFileSchedule.ReplacementFileBureauStatusId != null)
                        //    {
                        //        replacementFileSchedule.ReplacementFileBureauStatusId = ReplacementFileBureauStatuses.BureauLoadPending;
                        //    }
                        //}

                        var replacementFileAlgLeader = _dbContext.ALGClientLeaders
                            .Where(x => x.ClientId == replacementFileSchedule.ReplacementFileSubmission.Member.Id)
                            .Include(x => x.Leader)
                            .FirstOrDefault();

                        var replacementFileScheduleOutputDTO = new ReplacementFileScheduleOutputDTO
                        {
                            ActualSubmissionDate = replacementFileSchedule.ReplacementFileSubmission.ActualSubmissionDate == null
                            ? "N/A"
                            : replacementFileSchedule.ReplacementFileSubmission.ActualSubmissionDate?.ToString("dd-MM-yyyy"),
                            AlgLeader = replacementFileAlgLeader == null ? "N/A" : replacementFileAlgLeader.Leader.RegisteredName,
                            BureauReasonForUnsuccessfulLoad = replacementFileSchedule.UnnsuccessfulLoadReason == null ? "N/A" : replacementFileSchedule.UnnsuccessfulLoadReason.Name,
                            MemberName = replacementFileSchedule.ReplacementFileSubmission.Member.RegisteredName,
                            NumberOfRecords = replacementFileSchedule.ReplacementFileSubmission.NumberOfRecords,
                            ProposedSubmissionDate = replacementFileSchedule.ReplacementFileSubmission.PlannedSubmissionDate.ToString("dd-MM-yyyy"),
                            ReplacementFileName = replacementFileSchedule.ReplacementFileSubmission.ReplacementFileName,
                            ReSubmissionReason = replacementFileSchedule.ReplacementFileSubmission.ReplacementFileSubmissionReason.Name,
                            SACRRAAccountType = replacementFileSchedule.ReplacementFileSubmission.SACRRAAccountType,
                            SACRRAIndustry = EnumHelper.GetEnumIdValuePair<IndustryClassifications>((int)replacementFileSchedule.ReplacementFileSubmission.Member.IndustryClassificationId).Value,
                            SRNDisplayName = replacementFileSchedule.ReplacementFileSubmission.SRN == null ? "N/A" : replacementFileSchedule.ReplacementFileSubmission.SRN.TradingName,
                            SRNNumber = replacementFileSchedule.ReplacementFileSubmission.SRN == null ? "N/A" : replacementFileSchedule.ReplacementFileSubmission.SRN.SRNNumber,
                            StakeholderManager = replacementFileSchedule.ReplacementFileSubmission.Member.StakeholderManager.FullName,
                            SubmissionStatus = EnumHelper.GetEnumIdValuePair<ReplacementFileSubmissionStatuses>(replacementFileSchedule.ReplacementFileSubmission.ReplacementFileSubmissionStatusId).Value,
                            SubmissionStatusDate = replacementFileSchedule.ReplacementFileSubmission.SubmissionStatusDate.ToString("dd-MM-yyyy"),
                            BureauLoadStatus = replacementFileSchedule.ReplacementFileBureauStatusId == null ? "N/A" : EnumHelper.GetEnumIdValuePair<ReplacementFileBureauStatuses>((int)replacementFileSchedule.ReplacementFileBureauStatusId).Value,
                            BureauName = replacementFileSchedule.Bureau == null ? "N/A" : replacementFileSchedule.Bureau.RegisteredName,
                            RequestDate = replacementFileSchedule.ReplacementFileSubmission.CreatedAt.ToString("dd-MM-yyyy")
                        };

                        replacementFileScheduleOutputDTOList.Add(replacementFileScheduleOutputDTO);
                    }

                    //_dbContext.SaveChanges();
                    return replacementFileScheduleOutputDTOList;
                }

                return new List<ReplacementFileScheduleOutputDTO>();
            }
            catch (Exception ex)
            {
                throw new ReplacementFileScheduleException(0, null, ex.StackTrace);
            }
        }

        public async Task<ReplacementFileSubmissionOutputDTO> GetReplacementFileByName(string FileName)
        {
            var ReplacementFileOutput = new ReplacementFileSubmissionOutputDTO();
            var replacementFileSubmissions = _dbContext.ReplacementFileSubmissions
                .Include(x => x.SRN)
                .Include(x => x.Member)
                .ThenInclude(x => x.StakeholderManager)
                .FirstOrDefault(x => x.ReplacementFileName == FileName);

            if (replacementFileSubmissions != null)
            {
                ReplacementFileOutput.Id = replacementFileSubmissions.Id;
                ReplacementFileOutput.NumberOfFiles = replacementFileSubmissions.NumberOfFiles;
                ReplacementFileOutput.NumberOfRecords = replacementFileSubmissions.NumberOfRecords;
                ReplacementFileOutput.SRNNumber = replacementFileSubmissions.SRN == null ? "N?A" : replacementFileSubmissions.SRN.SRNNumber;
                ReplacementFileOutput.ReplacementFileName = replacementFileSubmissions.ReplacementFileName;
                ReplacementFileOutput.MemberName = replacementFileSubmissions.Member.RegisteredName;
                ReplacementFileOutput.FileSubmissionReason = _dbContext.ReplacementFileSubmissionReasons.Where(reason => reason.Id == replacementFileSubmissions.ReplacementFileSubmissionReasonId).FirstOrDefault().Name;
                ReplacementFileOutput.SRNDisplayName = replacementFileSubmissions.SRN == null ? "N/A" : replacementFileSubmissions.SRN.TradingName;
                ReplacementFileOutput.StakeHolderManager = replacementFileSubmissions.Member.StakeholderManager.FullName;
                ReplacementFileOutput.PlannedSubmissionDate = replacementFileSubmissions.PlannedSubmissionDate.ToString("yyyy-MM-dd");
                ReplacementFileOutput.OriginalFileName = replacementFileSubmissions.FileName;
                ReplacementFileOutput.SPNumber = replacementFileSubmissions.SRN == null ? _dbContext.SPGroups.Where(x => x.Id == replacementFileSubmissions.SPId).FirstOrDefault().SPNumber : "N/A";
                ReplacementFileOutput.ActualSubmissionDate = replacementFileSubmissions.ActualSubmissionDate == null ? "N/A" : replacementFileSubmissions.ActualSubmissionDate?.ToString("yyyy-MM-dd");

                return ReplacementFileOutput;
            }
            else
            {
                return ReplacementFileOutput;
            }
        }
    }
}