using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Sacrra.Membership.Api.Test.FakeGeneration;
using Sacrra.Membership.Api.Test.Helper;
using Sacrra.Membership.Api.Test.IntegrationTests;
using Sacrra.Membership.Business.DTOs.SRNUpdateDTOs;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Repositories;
using Sacrra.Membership.Business.Resources.Camunda.Task;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Camunda.CamundaAgents;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Database;
using Sacrra.Membership.Business.DTOs;

namespace Sacrra.Membership.Api.Test.EditSRNTests
{
    public class EditSRNTests : BaseIntegrationTest
    {
        private SRNService _srnService;
        private SrnUpdateDetailsCamundaAgent _srnUpdateDetailsCamundaAgent;
        private CamundaClient _camundaClient;
        private CamundaRepository _camundaRepository;
        private FakeSrnGeneration _fakeSRNGenrate;
        private readonly string _editDailySrn = "./EditSRNTests/Data/EditDailySRN.json";
        private readonly string _editMonthlySrn = "./EditSRNTests/Data/EditMonthlySRN.json";
        private readonly string _editDailyAndMonthlySrn = $"./EditSRNTests/Data/EditDailyAndMonthlySRN.json";

        [SetUp]
        public void Setup()
        {
            BaseSetup();

            BaseSetup();
            _srnService = GetService<SRNService>(_scope);
            _camundaRepository = GetService<CamundaRepository>(_scope);

            var configSettigs = _scope.ServiceProvider.GetService<IOptions<ConfigSettings>>();
            var httpClient = new HttpClient();
            httpClient.BaseAddress = new Uri(configSettigs!.Value.CamundaBaseAddress);

            _camundaClient = CamundaClient.Create(httpClient);
            _srnUpdateDetailsCamundaAgent = new SrnUpdateDetailsCamundaAgent(_camundaClient);
        }

        [Test]
        [Ignore("Comment out for Scratchpad")]
        public void Scratchpad()
        {
            //var srnId = 409587;
            // MakeItLiveOnDTH(srnId);
        }

        [Test, Description("Test editing for daily file")]
        public void TestUpdateSrnDetails_Daily()
        {
            TestUpdateDetails(_editDailySrn);
        }

        [Test, Description("Test editing for monthly file")]
        public void TestUpdateSrnDetails_Monthly()
        {
            TestUpdateDetails(_editMonthlySrn);
        }

        [Test, Description("Test editing for monthly file")]
        public void TestUpdateSrnDetails_DailyAndMonthly()
        {
            TestUpdateDetails(_editDailyAndMonthlySrn);
        }

        private void TestUpdateDetails(string jsonFilePath)
        {
            // Get SHM user
            var user = GetUserWithStakeholderManagerRole();


            var empty_date = new DateTime();

            // Then Change Details
            var srnUpdateInputDTO = JsonHelper.DeserializeFile<SRNUpdateInputDTO>(jsonFilePath);
            srnUpdateInputDTO.SRNDisplayName = "Test-SRN-Update-Details" + Guid.NewGuid();
            srnUpdateInputDTO.Id = 408654;

            Console.WriteLine($"Changed srn data: {srnUpdateInputDTO}");

            var testSRN = _srnService.UpdateSRN(srnUpdateInputDTO, user).Result;

            ProcessExternalTask(_srnUpdateDetailsCamundaAgent, "apply-srn-details-update");

            var dbContext = GetService<AppDbContext>(_scope);
            var dbSrn = dbContext.SRNs.FirstOrDefault(s => s.Id == srnUpdateInputDTO.Id);

            Console.WriteLine($"db val: {dbSrn}");

            Assert.That(dbSrn, Is.Not.Null);
            Assert.That(dbSrn.SRNNumber, Is.Not.Null);
            Assert.That(dbSrn.TradingName, Is.EqualTo(srnUpdateInputDTO.SRNDisplayName));
            Assert.That(dbSrn.FileType, Is.EqualTo(srnUpdateInputDTO.FileType));

        }

        private void ProcessExternalTask(ICamundaAgent camundaAgent, string topicName)
        {
            var workerId = "NUnit" + Guid.NewGuid();
            var externalTaskList = CamundaTaskHelper.FetchAndLockExternalTasks(_camundaClient, topicName, workerId);
            foreach (var externalTask in externalTaskList)
            {
                var completeExternalTask = new CompleteExternalTask() { WorkerId = workerId };
                camundaAgent.Process(topicName, externalTask, completeExternalTask, _scope).Wait();
            }
        }
    }
}
