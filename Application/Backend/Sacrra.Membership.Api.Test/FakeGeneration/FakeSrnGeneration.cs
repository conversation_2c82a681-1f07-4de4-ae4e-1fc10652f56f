using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Api.Test.IntegrationTests;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Database;

namespace Sacrra.Membership.Api.Test.FakeGeneration
{
    public class FakeSrnGeneration : BaseIntegrationTest
    {
        private SRNService _service;

        [SetUp]
        public void Setup()
        {
            BaseSetup();
            var service = _scope.ServiceProvider.GetService<SRNService>();
            _service = service ?? throw new NullReferenceException("Service not found");
        }

        [Test]
        [Ignore("Manual Test")]
        public void CreateTestSRN()
        {
            var user = GetUserWithStakeholderManagerRole();
            var srnRequestDto = new List<SRNRequestInputDTO> { GetFakeSRNRequest() };
            _service.CreateSrnEntries(srnRequestDto, user);
        }

        [Test]
        [Ignore("Manual Test")]
        public void DeleteTestSrns()
        {
            var dbContext = _factory.Services.GetService<AppDbContext>();
            if (dbContext == null)
                throw new Exception("Database context is null");
            var srns = dbContext.SRNs.Where(s => s.TradingName.StartsWith("TestSRN12345-")).ToList();
            if (srns != null)
            {
                dbContext.SRNs.RemoveRange(srns);
                dbContext.SaveChanges();
            }
        }

        private static SRNRequestInputDTO GetFakeSRNRequest()
        {
            List<ContactInputDTO> contactList = GetFakeContacts();

            var srnRequestDto = new SRNRequestInputDTO()
            {
                IsDailyFile = true,
                IsMonthlyFile = false,
                LoanManagementSystemId = 0,
                ThirdPartyVendorId = 0,
                AccountTypeId = 2,
                MemberId = 2528,
                NCRReportingAccountTypeClassificationId = 11,
                BillingCycle = 1,
                Contacts = contactList,
                SPNumberId = 0,
                BranchLocations = new List<BranchLocationSRNRequestDTO>(),
                ALGLeaderId = null,
                FileType = 0,
                NumberOfAccounts = 1113,
                SRNDisplayName = "TestSRN12345-" + Guid.NewGuid().ToString(),
                CreditInformationClassificationId = 1,
                DailyFileDevelopmentStartDate = DateTime.Now,
                DailyFileDevelopmentEndDate = DateTime.Now,
                DailyFileTestStartDate = DateTime.Now,
                DailyFileTestEndDate = DateTime.Now,
                DailyFileGoLiveDate = DateTime.Now,
                MonthlyFileDevelopmentStartDate = null,
                MonthlyFileDevelopmentEndDate = null,
                MonthlyFileTestStartDate = null,
                MonthlyFileTestEndDate = null,
                MonthlyFileGoLiveDate = null
            };
            return srnRequestDto;
        }

        private static List<ContactInputDTO> GetFakeContacts()
        {
            return new List<ContactInputDTO>()
                {
                    new ContactInputDTO()
                    {
                        ContactTypeId = 4,
                        FirstName = "THATO",
                        LastName = "TEST",
                        Designation = "COMPLIANCE",
                        OfficeNumber = "0877013254",
                        CellphoneNumber = "0877013254",
                        EmailAddress = "<EMAIL>"
                    },
                    new ContactInputDTO()
                    {
                        ContactTypeId = 5,
                        FirstName = "THATO",
                        LastName = "DATA",
                        Designation = "DATA",
                        OfficeNumber = "0877013254",
                        CellphoneNumber = "0877013254",
                        EmailAddress = "<EMAIL>"
                    },
                    new ContactInputDTO()
                    {
                        ContactTypeId = 6,
                        FirstName = "NEW ",
                        LastName = "TEST",
                        Designation = "MANUAL",
                        OfficeNumber = "0877013254",
                        CellphoneNumber = "0877013254",
                        EmailAddress = "<EMAIL>"
                    },
                    new ContactInputDTO()
                    {
                        ContactTypeId = 7,
                        FirstName = "NEW ",
                        LastName = "DATA",
                        Designation = "DTH",
                        OfficeNumber = "0877013254",
                        CellphoneNumber = "0877013254",
                        EmailAddress = "<EMAIL>"
                    }
                };
        }
    }
}
