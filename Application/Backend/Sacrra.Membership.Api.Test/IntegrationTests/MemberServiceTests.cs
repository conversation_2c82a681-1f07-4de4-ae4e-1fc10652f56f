using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Business.Services.MembersService;
using Sacrra.Membership.Database.Models;

namespace Sacrra.Membership.Api.Test.IntegrationTests;

public class MemberServiceTests : BaseIntegrationTest
{
    private MembersService _service;

    [SetUp]
    public void Setup()
    {
        BaseSetup();
        var service = _scope.ServiceProvider.GetService<MembersService>();
        _service = service ?? throw new NullReferenceException("Service not found");
    }

    [Test]
    public void TestGetMyInformation_ALGLeader()
    {
        var user = GetUserWithALGLeaderRole();
        TestGetMyInformation(user);
    }

    [Test]
    public void TestGetMyInformation_Member()
    {
        var user = GetUserWithMemberRole();
        TestGetMyInformation(user);
    }

    [Test]
    public void TestGetMyInformation_Bureau()
    {
        var user = GetUserWithBureauRole();
        TestGetMyInformation(user);
    }

    [Test]
    public void TestGetMyInformation_User()
    {
        var user = GetUserWithUserRole();
        var results = _service.GetMyInformation(user);
        Assert.That(results.Count, Is.EqualTo(0));
    }

    [Test]
    public void TestGetMyInformation_StakeholderManager()
    {
        var user = GetUserWithStakeholderManagerRole();
        TestGetMyInformation(user);
    }

    [Test]
    public void TestGetAllMemberContactsForFreshdesk()
    {
        var result = _service.GetAllMemberContactsForFreshdesk();
        Assert.That(result.Count(), Is.GreaterThan(0));
    }

    private void TestGetMyInformation(User user)
    {
        var results = _service.GetMyInformation(user);
        Assert.That(results.Count(), Is.GreaterThan(0));
    }
}