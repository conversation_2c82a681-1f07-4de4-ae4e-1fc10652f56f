{
  "Logging": {
    "LogLevel": {
      "Default": "Warning",
      "Hangfire": "Error"
    }
  },
  "Cronitor": {
    "RumKey": "670dbcae9cd678ac4e47b1a747b0d45f"
  },
  "KeyVault": {
    "Vault": "sacrra-prod-vault",
    "ClientId": "78f7de58-8ecc-470b-8768-ae922243a0fe",
    "TenantId": "3040f3fa-8953-40f9-ab70-055b6b2af644"
  },
  "Auth0": {
    "Domain": "login.sacrra.org.za",
    "Audience": "https://sacrra-prod.southafricanorth.cloudapp.azure.com/api",
    "ClientID": "QlRvP2WupSRN8o4F7OxRdhfcNIXxu12i",
    "WebAppClientID": "bdtOSienVKAW8NSKH5zVl3wwAcwHBqVQ",
    "GrantType": "password",
    "ClaimsNamespace": "https://sacrra-prod.southafricanorth.cloudapp.azure.com/claims/roles"
  },
  "Auth0APIManagement": {
    "Domain": "login.sacrra.org.za",
    "Audience": "https://sacrra-production.eu.auth0.com/api/v2/",
    "ClientID": "QlRvP2WupSRN8o4F7OxRdhfcNIXxu12i",
    "Connection": "Username-Password-Authentication",
    "APIBaseURL": "https://login.sacrra.org.za/api/v2"
  },
  "ConfigSettings": {
    "ApiBaseUri": "/api/",
    "CamundaBaseAddress": "http://sacrra.camunda:8080/engine-rest/engine/default",
    "FrontEndBaseUri": "https://portal.sacrra.org.za",
    "CreateInternalUsersInAuth0AtStartup": false,
    "CamundaAdminUserName": "sacrra",
    "CamundaAdminEmail": "<EMAIL>",
    "Auth0LoginTemplate": "auth0logintemplate.html",
    "ConfigTemplatesPath": "configtemplates",
    "FTPServerAddress": "hub.sacrra.org.za",
    "FTPServerPort": 22,
    "FTPServerUser": "SACRRAConnectPRD",
    "CamundaConnectionString": "Host=sacrra-automation-prod-postgres-server.postgres.database.azure.com;SSL Mode=Require;Username=ewx@sacrra-automation-prod-postgres-server;Database=Sacrra.Camunda;Password=************;Port=5432;Trust Server Certificate=True"
  },
  "EmailSettings": {
    "SmtpUsername": "<EMAIL>",
    "EmailFromName": "South African Credit & Risk Reporting Association (SACRRA)",
    "EmailFromAddress": "<EMAIL>",
    "SmtpServer": "smtp.mailgun.org",
    "SmtpPort": 587,
    "SmtpEnableSsl": "true",
    "EmailTemplateFilePath": "mailtemplates/",
    "IsEmailEnabled": true,
    "SendEmailOnEnv": true,
    "AdminEmail": "<EMAIL>",
    "RetryLimit": 5
  },
  "MailgunAPI": {
    "BaseURL": "https://api.mailgun.net/v3",
    "Domain": "mailgun.sacrra.org.za",
    "EventPollingLimit": 25,
    "EventPollingSortOrder": "descending", //Possible values: descending|ascending
    "EventPollingTime": "17:00"
  },
  "FreshdeskIntegration": {
    "BaseURL": "https://newaccount1635241009069.freshdesk.com",
    "Domain": "newaccount1635241009069.freshdesk.com",
    "TicketQueryFilter": "(tag:bureaumeeting)",
    "Webhook": {
      "TicketQueryFilter": "(tag:bureaumeeting)"
    }
  },
  "Serilog": {
    "Using": [
      "Serilog.Sinks.AzureBlobStorage"
    ],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Error",
        "Sacrra.Membership.Api.CamundaAgent": "Information",
        "Microsoft.EntityFrameworkCore.Database.Command": "Error",
        "Hangfire": "Error",
        "Sacrra.Membership.Api.Filters.LogActionFilter": "Error",
        "Microsoft.AspNetCore.Server.Kestrel": "Fatal",
        "Sacrra.Membership.Business.Services.SRNService": "Error"
      }
    },
    "WriteTo": [
      {
        "Name": "Syslog",
        "Args": {
          "server": "logs4.papertrailapp.com",
          "port": "10287",
          "application": "sacrra-membership-production",
          "outputTemplate": "{Timestamp:HH:mm:ss} [{Level:u3}] {Message:lj}{NewLine}{Exception}"
        }
      }
    ],
    "Properties": {
      "Application": "Sacrra.Membership",
      "Environment": "Production"
    }
  },
  "ReportingAPISettings": {
    "BaseApiUrl": "https://sacreportvm.southafricanorth.cloudapp.azure.com:8081",
    "Dataset": "SacrraProd"
  },
  "Documents": {
    "StorageContainerName": "documents" //Container where the documents entries will be written to.
  },
  "MemberInvoice": {
    "BillingStartDay": "15",
    "BillingLastDay": "14"
  }
}
