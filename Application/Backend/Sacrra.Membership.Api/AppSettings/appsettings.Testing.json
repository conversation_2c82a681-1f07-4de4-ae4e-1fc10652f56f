{"ConnectionString": "Server=localhost;Integrated Security=true;Database=Sacrra.Membership", "ConfigSettings": {"CamundaBaseAddress": "http://localhost:8082/engine-rest/engine/default"}, "EmailSettings": {"SmtpUsername": "", "EmailFromName": "Testing South African Credit & Risk Reporting Association (SACRRA)", "EmailFromAddress": "<EMAIL>", "SmtpPassword": "", "SmtpServer": "localhost", "SmtpPort": 8025, "SmtpEnableSsl": "false", "EmailTemplateFilePath": "mailtemplates/", "IsEmailEnabled": false, "SendEmailOnEnv": false, "AdminEmail": "<EMAIL>", "RetryLimit": 5}, "Serilog": {"MinimumLevel": {"Default": "Debug", "Override": {"Microsoft": "Error", "Sacrra.Membership.Api.CamundaAgent": "Information", "Microsoft.EntityFrameworkCore.Database.Command": "Error", "Hangfire": "Error", "Sacrra.Membership.Api.Filters.LogActionFilter": "Debug", "Microsoft.AspNetCore.Server.Kestrel": "Fatal"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console", "outputTemplate": "{Timestamp:HH:mm:ss} [{Level:u3}] {Message:lj}{NewLine}{Exception}"}}]}}