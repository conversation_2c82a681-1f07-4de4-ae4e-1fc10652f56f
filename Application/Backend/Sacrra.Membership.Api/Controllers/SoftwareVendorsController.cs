using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Api.Helpers;
using Sacrra.Membership.Business.Exceptions;
using Sacrra.Membership.Business.ListParams;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Business.Resources.SoftwareVendor;
using System.Collections.Generic;
using System.Threading.Tasks;
using Sacrra.Membership.Business.Repositories;

namespace Sacrra.Membership.Api.Controllers
{
    [Produces("application/json")]
    [ApiController]
    [Route("api/v{v:apiVersion}/[controller]")]
    public class SoftwareVendorsController : Controller
    {
        private readonly SoftwareVendorRepository _repository;

        public SoftwareVendorsController(SoftwareVendorRepository SoftwareVendorRepository)
        {
            _repository = SoftwareVendorRepository;
        }

        [Authorize]
        [HttpGet("{id}", Name = "GetSoftwareVendor")]
        [ProducesResponseType(typeof(SoftwareVendorGetResource), 200)]
        [ProducesResponseType(typeof(SoftwareVendorGetResource), 404)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> Get(int id)
        {
            var entity = await _repository.Get(id);
            if (entity == null)
                return NotFound();

            return Ok(entity);
        }

        [Authorize]
        [HttpGet]
        [ProducesResponseType(type: typeof(List<IdValuePairResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> List([FromQuery]NameListParams listParams)
        {
            var SoftwareVendors = await _repository.List(listParams);

            Response.AddPagination(SoftwareVendors.CurrentPage, SoftwareVendors.PageSize, SoftwareVendors.TotalCount, SoftwareVendors.TotalPages);

            return Ok(SoftwareVendors);
        }

        [Authorize(Roles = "Group Stakeholder Manager,Financial Administrator,User,Member,Stakeholder Manager,SACRRA Administrator,Bureau,ALG Leader")]
        [HttpPost]
        [ProducesResponseType(typeof(int), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> Create([FromBody]SoftwareVendorCreateResource modelForCreate)
        {
            if (!ModelState.IsValid)
            {
                var errorModel = new ApiErrorResource
                {
                    Id = 0,
                    Message = "Software Vendor Name is required",
                    StatusCode = 400
                };
                return BadRequest(errorModel);
            }

            var id = await _repository.Create(modelForCreate);

            return CreatedAtRoute("GetSoftwareVendor", new { controller = "SoftwareVendors", id }, id);
        }

        [Authorize(Roles = "Group Stakeholder Manager,Financial Administrator,User,Member,Stakeholder Manager,SACRRA Administrator,Bureau,ALG Leader")]
        [HttpPut("{id}")]
        [ProducesResponseType(typeof(SoftwareVendorGetResource), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]

        public async Task<IActionResult> Update(int id, [FromBody]SoftwareVendorUpdateResource modelForUpdate)
        {
            if (!ModelState.IsValid)
            {
                var errorModel = new ApiErrorResource
                {
                    Id = 0,
                    Message = "Software Vendor Name is required",
                    StatusCode = 400
                };
                return BadRequest(errorModel);
            }

            return Ok(await _repository.Update(modelForUpdate));
        }

        [Authorize(Roles = "Group Stakeholder Manager,Financial Administrator,User,Member,Stakeholder Manager,SACRRA Administrator,Bureau,ALG Leader")]
        [HttpDelete("{id}")]
        [ProducesResponseType(typeof(void), 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<IActionResult> Delete(int id)
        {
            await _repository.Delete(id);
            return Ok();
        }
    }
}
