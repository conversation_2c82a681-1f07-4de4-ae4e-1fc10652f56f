using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Serilog;
using System;
using System.Threading.Tasks;

namespace Sacrra.Membership.Api.Controllers
{
    [Authorize]
    [Produces("application/json")]
    [ApiController]
    [Route("api/v{v:apiVersion}/[controller]")]
    public class TestingController
    {
        [HttpGet("dummy-error")]
        public Task<IActionResult> DummyError()
        {
            try
            {
                throw new Exception("Dummy error");
            }
            catch(Exception ex)
            {
                Log.Error(ex, "Testing dummy error");
                throw new Exception("Testing dummy error");
            }
        }
    }
}
