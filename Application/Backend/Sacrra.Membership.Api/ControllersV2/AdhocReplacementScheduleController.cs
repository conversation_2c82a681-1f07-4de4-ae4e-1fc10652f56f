using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Business.DTOs.AdhocRplacementScheduleDTOs;
using Sacrra.Membership.Business.Services.AdhocReplacementScheduleService;
using System.Collections.Generic;

namespace Sacrra.Membership.Api.ControllersV2
{
    [Authorize(Roles = "Member, ALG Leader, Stakeholder Manager, Bureau, SACRRA Administrator")]
    [ApiController]
    [ApiVersion("2")]
    [Route("api/v{v:apiVersion}/[controller]/[action]")]
    public class AdhocReplacementScheduleController : Controller
    {
        private AdhocReplacementScheduleService _adhocReplacementScheduleService;

        public AdhocReplacementScheduleController(AdhocReplacementScheduleService adhocReplacementScheduleService)
        {
            _adhocReplacementScheduleService = adhocReplacementScheduleService;
        }

        [HttpGet]
        [ProducesResponseType(typeof(List<AdhocReplacementScheduleOutputDTO>), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public List<AdhocReplacementScheduleOutputDTO> GetReplacementSchedule()
        {
            return _adhocReplacementScheduleService.GetReplacementFilesSchedule(User);
        }

        [HttpGet]
        [ProducesResponseType(typeof(List<AdhocReplacementScheduleOutputDTO>), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public List<AdhocReplacementScheduleOutputDTO> GetAdhocSchedule()
        {
            return _adhocReplacementScheduleService.GetAdhocFilesSchedule(User);
        }
    }
}
