using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Business.Resources.IdValuePair;
using System.Collections.Generic;

namespace Sacrra.Membership.Api.ControllersV2
{
    [ApiController]
    [ApiVersion("2")]
    [Route("api/v{v:apiVersion}/[controller]/[action]")]
    public class CreditInformationClassificationsController : Controller
    {
        private readonly CreditInformationClassificationService _creditInfoClassificationsService;

        public CreditInformationClassificationsController (CreditInformationClassificationService creditInfoClassificationsService)
        {
            _creditInfoClassificationsService = creditInfoClassificationsService;
        }

        [Authorize]
        [HttpGet]
        [ProducesResponseType(type: typeof(List<IdValuePairResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetCreditInformationClassifications()
        {
            var CreditInformationClassificans = _creditInfoClassificationsService.GetAll();

            return Ok(CreditInformationClassificans);
        }
    }
}
