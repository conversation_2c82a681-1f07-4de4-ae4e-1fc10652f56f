using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Business.Resources.IdValuePair;
using System.Collections.Generic;

namespace Sacrra.Membership.Api.ControllersV2
{
    [Authorize(Roles = "Financial Administrator,Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator")]
    [ApiController]
    [ApiVersion("2")]
    [Route("api/v{v:apiVersion}/[controller]/[action]")]
    public class DocumentStatusesController : Controller
    {
        private readonly DocumentStatusService _statusService;
        public DocumentStatusesController(DocumentStatusService statusService)
        {
            _statusService = statusService;
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(IEnumerable<IdValuePairResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetStatusList()
        {
            var statuses = _statusService.GetList();
            return Ok(statuses);
        }
    }
}
