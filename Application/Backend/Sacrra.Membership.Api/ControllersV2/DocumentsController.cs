using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.Services;
using System;
using System.Collections.Generic;

namespace Sacrra.Membership.Api.ControllersV2
{
    [ApiController]
    [ApiVersion("2")]
    [Route("api/v{v:apiVersion}/[controller]/[action]")]
    public class DocumentsController : Controller
    {
        private readonly DocumentsService _documentsService;
        private readonly GlobalHelper _globalHelper;
        public DocumentsController(DocumentsService documentsService, GlobalHelper globalHelper)
        {
            _documentsService = documentsService;
            _globalHelper = globalHelper;
        }

        [Authorize(Roles = "SACRRA Administrator")]
        [HttpPost]
        [ProducesResponseType(type: typeof(void), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CreateDocument([FromBody] DocumentInputDTO inputDTO)
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            _documentsService.Create(inputDTO, user);
            return Ok();
        }

        [Authorize(Roles = "SACRRA Administrator")]
        [HttpPut("{id}")]
        [ProducesResponseType(type: typeof(void), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult UpdateDocument(int id, [FromBody] DocumentInputDTO inputDTO)
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            _documentsService.Update(id, inputDTO, user);
            return Ok();
        }

        [Authorize(Roles = "Financial Administrator,Member,Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator,Bureau,ALG Leader")]
        [HttpGet]
        [ProducesResponseType(type: typeof(IEnumerable<DocumentOutputDTO>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetDocumentList()
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            var documents = _documentsService.GetList(user);
            return Ok(documents);
        }

        [Authorize(Roles = "Financial Administrator,Member,Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator,Bureau,ALG Leader")]
        [HttpGet("{id}")]
        [ProducesResponseType(type: typeof(DocumentOutputSingleDTO), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetDocument(int id)
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            var document = _documentsService.Get(id, user);
            if(document == null)
            {
                return NotFound();
            }
            return Ok(document);
        }

        [Authorize(Roles = "SACRRA Administrator")]
        [HttpDelete("{id}")]
        [ProducesResponseType(type: typeof(IEnumerable<DocumentOutputDTO>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult RemoveDocument(int id)
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            var documents = _documentsService.Delete(id, user);
            return Ok(documents);
        }

        [Authorize(Roles = "Financial Administrator,Member,Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator,Bureau,ALG Leader")]
        [HttpGet]
        [ProducesResponseType(type: typeof(DocumentUserAccessOutputDTO), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetDocumentUserAccess()
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            var documentAccess = _documentsService.GetDocumentUserAccess(user);
            return Ok(documentAccess);
        }

        [Authorize(Roles = "Financial Administrator,Member,Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator,Bureau,ALG Leader, User")]
        [HttpGet]
        [ProducesResponseType(type: typeof(DateTime?), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetTsAndCsLastUpdatedDate()
        {
            var lastUpdatedAt = _documentsService.GetTsAndCsLastUpdatedDate();
            return Ok(lastUpdatedAt);
        }
    }
}
