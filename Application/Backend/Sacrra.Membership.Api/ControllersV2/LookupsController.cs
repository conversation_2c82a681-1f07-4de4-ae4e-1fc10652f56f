using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Business.DTOs.ReplacementFileSubmissionDTOs;
using Sacrra.Membership.Business.Services.LookupsService;
using Sacrra.Membership.Business.Resources.IdValuePair;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Freshdesk.Enums;
using System.Collections.Generic;
using System.Threading.Tasks;
using Sacrra.Membership.Business.DTOs.AdhocFileSubmissionDTOs;
using Sacrra.Membership.Business.Services.AdhocFilesService;

namespace Sacrra.Membership.Api.ControllersV2
{
    [Authorize(Roles = "Financial Administrator,User,Member,Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator,System Administrator,ALG Leader,Bureau")]
    [ApiController]
    [ApiVersion("2")]
    [Route("api/v{v:apiVersion}/[controller]/[action]")]
    public class LookupsController: Controller
    {
        private LookupsService _lookupsService;

        public LookupsController(LookupsService lookupsService)
        {
            _lookupsService = lookupsService;
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(IdValuePairResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public List<IdValuePairResource> GetSHMMemberReviewDecisions()
        {
            return _lookupsService.GetEnumIdValuePairs<MemberReviewDecisionEnum>();
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(IdValuePairResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public List<IdValuePairResource> GetSHMALGClientReviewDecisions()
        {
            return _lookupsService.GetEnumIdValuePairs<ALGClientReviewDecisionEnum>();
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(IdValuePairResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public List<IdValuePairResource> GetMembershipTypesForALGClientUpdateTask()
        {
            return _lookupsService.GetMembershipTypesForALGClientUpdateTask<MembershipTypes>(User);
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(IdValuePairResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public List<IdValuePairResource> GetMembershipTypesForMemberUpdateTask()
        {
            return _lookupsService.GetMembershipTypesForMemberUpdateTask<MembershipTypes>(User);
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(IdValuePairResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public List<IdValuePairResource> GetMembershipTypesById(MembershipTypes membershipTypeId)
        {
            return _lookupsService.GetMembershipTypesById<MembershipTypes>(membershipTypeId);
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(IdValuePairResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public List<IdValuePairResource> GetFreshdeskTicketStatuses()
        {
            return _lookupsService.GetEnumIdValuePairs<TicketStatusEnum>();
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(IdValuePairResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public List<IdValuePairResource> GetOSLAReasons()
        {
            return _lookupsService.GetOSLAReasons();
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(IdValuePairResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public List<IdValuePairResource> GetReplacementFileSubmissionStatuses()
        {
            return _lookupsService.GetEnumIdValuePairs<ReplacementFileSubmissionStatuses>();
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(ReplacementFileSubmissionCategory), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public List<IdValuePairResource> GetReplacementFileSubmissionCategories()
        {
            return _lookupsService.GetReplacementFileSubmissionCategories();
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(IdValuePairResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public List<IdValuePairResource> GetReplacementFileCancellationReason()
        {
            return _lookupsService.GetEnumIdValuePairs<ReplacementFileReasonForCancellation>();
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(IdValuePairResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public List<IdValuePairResource> GetReplacementFileTypes()
        {
            return _lookupsService.GetEnumIdValuePairs<ReplacementFileTypes>();
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(IdValuePairResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public List<IdValuePairResource> GetAhocFileTypes()
        {
            return _lookupsService.GetEnumIdValuePairs<AdHocFileTypes>();
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(IdValuePairResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public List<IdValuePairResource> GetBureauUnsuccessfulReplacementLoadReasons()
        {
            return _lookupsService.GetReplacementFileSubmissionReasons();
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(IdValuePairResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public List<IdValuePairResource> GetBureauUnsuccessfulAdHocLoadReasons()
        {
            return _lookupsService.GetBureauUnsuccessfulAdHocLoadReasons();
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(IdValuePairResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public List<IdValuePairResource> GetAdhocFileSubmissionReasons()
        {
            return _lookupsService.GetAdhocFileSubmissionReasons();
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(IdValuePairResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<List<IdValuePairResource>> GetSRNNumbersForCurrentUser()
        {
            return await _lookupsService.GetSRNNumbersForCurrentUser(User);
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(IdValuePairResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]        
        public List<IdValuePairResource> GetAdhocFileNames(string fileName = null)
        {
            return _lookupsService.GetAdhocFileNames(fileName);
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(IdValuePairResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<List<IdValuePairResource>> GetSRNDisplayNamesForCurrentUser()
        {
            return await _lookupsService.GetSRNDisplayNamesForCurrentUser(User);
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(IdValuePairResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public List<IdValuePairResource> GetReplacementFileSubmissionReasons()
        {
            return _lookupsService.GetReplacementFileSubmissionReasons();
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(IdValuePairResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public List<IdValuePairResource> GetReplacementFileSubmissionDeclineReasons()
        {
            return _lookupsService.GetEnumIdValuePairs<ReplacementFileSubmissionDeclineReasons>();
        }

        [HttpGet("{Id}")]
        [ProducesResponseType(type: typeof(IdValuePairResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public List<IdValuePairResource> GetUserMembers(int Id)
        {
            return _lookupsService.GetUserMembers(Id);
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(IdValuePairResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<List<IdValuePairResource>> GetSPNumbersForCurrentUser()
        {
            return await _lookupsService.GetSPNumbersForCurrentUser(User);
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(IdValuePairResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public List<IdValuePairResource> GetAllMembersInSystem()
        {
            return _lookupsService.GetAllStakeholderManagers();
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(IdValuePairResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public List<IdValuePairResource> GetAllStakeholderManagers()
        {
            return _lookupsService.GetAllStakeholderManagers();
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(IdValuePairResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public List<IdValuePairResource> GetAllALGLeaders()
        {
            return _lookupsService.GetAllALGLeaders();
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(IdValuePairResource), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public List<IdValuePairResource> GetAdHocSubmissionStatuses()
        {
            return _lookupsService.GetEnumIdValuePairs<ReplacementFileSubmissionStatuses>();
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(int), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public dynamic GetSRNFileStatuses([FromQuery] int srnId)
        {
            return _lookupsService.GetSRNFileStatuses(srnId);
        }
    }
}
