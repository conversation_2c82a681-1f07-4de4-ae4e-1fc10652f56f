using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Business.Services;
using System;

namespace Sacrra.Membership.Api.ControllersV2
{
    [Authorize(Roles = "Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator,System Administrator")]
    [ApiController]
    [ApiVersion("2")]
    [Route("api/v{v:apiVersion}/[controller]/[action]")]
    public class MailgunController : Controller
    {
        private readonly MailgunService _mailgunService;
        public MailgunController(MailgunService mailgunService)
        {
            _mailgunService = mailgunService;
        }

        [HttpGet("{eventType}")]
        [ProducesResponseType(type: typeof(string), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetEvents(string eventType, string recipient = null, DateTime? begin = null, DateTime? end = null)
        {
            var data = _mailgunService.GetEvents(eventType, recipient, begin, end);
            return Ok(data);
        }
        [HttpPost]
        [ProducesResponseType(type: typeof(string), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult CreateEventsCronJob()
        {
            _mailgunService.CreateMailgunEventsCronJob();
            return Ok();
        }
    }
}
