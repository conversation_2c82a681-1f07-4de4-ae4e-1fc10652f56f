using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Api.Helpers;
using Sacrra.Membership.Business.ListParams;
using Sacrra.Membership.Business.Resources.IdValuePair;
using System.Collections.Generic;

namespace Sacrra.Membership.Api.ControllersV2
{
    [ApiController]
    [ApiVersion("2")]
    [Route("api/v{v:apiVersion}/[controller]/[action]")]

    public class NCRReportingAccountTypeClassificationsController : Controller
    {
        private readonly NCRReportingAccountTypeClassificationsService _ncrAccountTypeClassificationsService;

        public NCRReportingAccountTypeClassificationsController(NCRReportingAccountTypeClassificationsService NCRAccountTypeClassificationsService)
        {
            _ncrAccountTypeClassificationsService = NCRAccountTypeClassificationsService;
        }

        [Authorize]
        [HttpGet]
        [ProducesResponseType(type: typeof(List<IdValuePairResource>), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public IActionResult GetNCRReportingAccountTypeClassifications()
        {
            var NCRReportingAccountTypeClassifications = _ncrAccountTypeClassificationsService.GetNCRReportingAccountTypeClassifications();

            return Ok(NCRReportingAccountTypeClassifications);
        }
    }
}
