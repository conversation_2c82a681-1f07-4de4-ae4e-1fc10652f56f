using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using RestSharp;
using Sacrra.Membership.Business.DTOs.ReplacementFileSubmissionDTOs;
using Sacrra.Membership.Business.Services.ReplacementFileSubmissionService;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Sacrra.Membership.Api.ControllersV2
{
    [Authorize(Roles = "Member, ALG Leader, Stakeholder Manager, Bureau")]
    [ApiController]
    [ApiVersion("2")]
    [Route("api/v{v:apiVersion}/[controller]/[action]")]
    public class ReplacementFileSubmissionController : Controller
    {
        private ReplacementFileSubmissionService _replacementFileSubmissionService;

        public ReplacementFileSubmissionController(ReplacementFileSubmissionService replacementFileSubmissionService)
        {
            _replacementFileSubmissionService = replacementFileSubmissionService;
        }

        [HttpPost]
        [ProducesResponseType(typeof(RestResponse), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<RestResponse> RequestReplacementFileSubmission([FromBody] ReplacementFileSubmissionInputDTO inputDTO)
        {
            return await _replacementFileSubmissionService.RequestReplacementFileSubmission(inputDTO);
        }

        [Authorize(Roles = "Bureau")]
        [HttpGet]
        [ProducesResponseType(typeof(List<ReplacementFileSubmissionOutputDTO>), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public List<ReplacementFileSubmissionOutputDTO> GetApprovedReplacementFiles()
        {
            return _replacementFileSubmissionService.GetApprovedReplacementFiles();
        }

        [Authorize(Roles = "Bureau")]
        [HttpPost]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task SubmitUnsuccessfulReplacementLoad([FromBody] BureauUnsuccessfulLoadInputDTO inputDTO)
        {
            await _replacementFileSubmissionService.SubmitUnsuccessfulReplacementLoad(inputDTO, User);
        }

        [Authorize(Roles = "Bureau")]
        [HttpPost]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public ActionResult SubmitSuccessfulLoad([FromBody] BureauSuccessfulLoadInputDTO data)
        {
            return _replacementFileSubmissionService.SetSuccessfulFileLoad(data);
        }

        [HttpGet]
        [ProducesResponseType(typeof(List<ReplacementFileScheduleOutputDTO>), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<List<ReplacementFileScheduleOutputDTO>> GetReplacementFileSchedule()
        {
            return await _replacementFileSubmissionService.GetReplacementFileSchedule(User);
        }

        [HttpPost]
        [ProducesResponseType(typeof(List<ReplacementFileScheduleOutputDTO>), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public async Task<ReplacementFileSubmissionOutputDTO> GetReplacementFileByName([FromBody] ReplacementFileSubmissionInputDTO inputDTO)
        {
            return await _replacementFileSubmissionService.GetReplacementFileByName(inputDTO.ReplacementFileName);
        }
    }
}
