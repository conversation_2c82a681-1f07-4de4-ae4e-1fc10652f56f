using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Sacrra.Membership.Business.Exceptions;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Models;
using Serilog;
using System;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;
using System.IO;
using System.Text;
using Microsoft.EntityFrameworkCore;
using System.Net;

namespace Sacrra.Membership.Api.Filters
{
    public class ApiExceptionFilter : ExceptionFilterAttribute
    {
        public override void OnException(ExceptionContext context)
        {
            var _dbContext = context.HttpContext.RequestServices.GetService<AppDbContext>();
            CustomApiException exception;
            var exceptionMessage = "";

            if (context.Exception is CustomApiException)
            {
                exceptionMessage = (context.Exception as CustomApiException).ExMessage;
            }

            switch (context.Exception)
            {
                case InvalidUserException _:
                    exception = new InvalidUserException((int)HttpStatusCode.Unauthorized, "Invalid username or password.", context.Exception.StackTrace);
                    break;

                case EmailNotConfirmedException _:
                    exception = new EmailNotConfirmedException((int)HttpStatusCode.Unauthorized, "Email not confirmed.", context.Exception.StackTrace);
                    break;

                case MemberExistsException _:
                    exception = new MemberExistsException((int)HttpStatusCode.BadRequest, "Member already exists. Update the existing member details if required.", context.Exception.StackTrace);
                    break;

                case UserExistsException _:
                    exception = new UserExistsException((int)HttpStatusCode.BadRequest, "User already exists.", context.Exception.StackTrace);
                    break;

                case InvalidUserNameException _:
                    exception = new InvalidUserNameException((int)HttpStatusCode.BadRequest, "Invalid username.", context.Exception.StackTrace);
                    break;

                case InvalidSRNSaleException _:
                    exception = new InvalidSRNSaleException((int)HttpStatusCode.BadRequest, "Unable to start SRN sale process. No SRN ID specified.", context.Exception.StackTrace);
                    break;

                case InvalidSRNSaleNoSHMException _:
                    exception = new InvalidSRNSaleNoSHMException((int)HttpStatusCode.BadRequest, "Unable to start SRN sale process. No SHM assigned to SRN.", context.Exception.StackTrace);
                    break;

                case InvalidSRNMergeNoSHMException _:
                    exception = new InvalidSRNMergeNoSHMException((int)HttpStatusCode.BadRequest, "Unable to start SRN merge process. No SHM assigned to SRN.", context.Exception.StackTrace);
                    break;

                case InvalidSRNMergeNoMergeListException _:
                    exception = new InvalidSRNMergeNoMergeListException((int)HttpStatusCode.BadRequest, "An SRN merge has been requested but there's no merge list specified.", context.Exception.StackTrace);
                    break;

                case InvalidSRNSplitNoSHMException _:
                    exception = new InvalidSRNSplitNoSHMException((int)HttpStatusCode.BadRequest, "Unable to start SRN split process. No SHM assigned to SRN.", context.Exception.StackTrace);
                    break;

                case InvalidSRNSplitNoSplitListException _:
                    exception = new InvalidSRNSplitNoSplitListException((int)HttpStatusCode.BadRequest, "An SRN split has been requested but there's no split list specified.", context.Exception.StackTrace);
                    break;

                case InvalidReportParametersException _:
                    exception = new InvalidReportParametersException((int)HttpStatusCode.BadRequest, "Invalid report parameters.", context.Exception.StackTrace);
                    break;

                case NoALGLeaderException _:
                    exception = new NoALGLeaderException((int)HttpStatusCode.BadRequest, "You must select at least ONE ALG Leader for ALG Client membership type", context.Exception.StackTrace);
                    break;

                case InvalidSRNCreateNoALGLeader _:
                    exception = new InvalidSRNCreateNoALGLeader((int)HttpStatusCode.NotFound, "An ALG client SRN must have linked to an ALG Leader", context.Exception.StackTrace);
                    break;

                case UnauthorizedException _:
                    exception = new UnauthorizedException((int)HttpStatusCode.Unauthorized, "User not authorized to access the requested resource", context.Exception.StackTrace);
                    break;

                case PasswordChangeRequiredException _:
                    exception = new PasswordChangeRequiredException((int)HttpStatusCode.Unauthorized, "User password change is required before signing in", context.Exception.StackTrace);
                    break;

                case InvalidSRNStatusUpdateException _:
                    exception = new InvalidSRNStatusUpdateException((int)HttpStatusCode.BadRequest, "Invalid SRN status update input. Verify that all required data is correct", context.Exception.StackTrace);
                    break;

                case SRNStatusUpdateInProgressException _:
                    exception = new SRNStatusUpdateInProgressException((int)HttpStatusCode.BadRequest, "SRN Status Update Not Allowed. SRN status update already in progress", context.Exception.StackTrace);
                    break;

                case SRNCreationException _:
                    exception = new SRNCreationException((int)HttpStatusCode.BadRequest, "Unable to create SRN. Please try again later.", context.Exception.StackTrace);
                    break;

                case MemberCreationException _:
                    exception = new MemberCreationException((int)HttpStatusCode.BadRequest, "Unable to create Member. Please try again later.", context.Exception.StackTrace);
                    break;

                case NCRCategoryException _:
                    exception = new NCRCategoryException((int)HttpStatusCode.BadRequest, "The NCR Fee Category and the Principle Debt Range calculations do not match.", context.Exception.StackTrace);
                    break;

                case VATNumberExistsException _:
                    exception = new VATNumberExistsException((int)HttpStatusCode.BadRequest, "VAT Number already exists. Update the existing member details if required.", context.Exception.StackTrace);
                    break;

                case SPCreationException _:
                    exception = new SPCreationException((int)HttpStatusCode.BadRequest, "Unable to create SP number. SP number cannot be empty.", context.Exception.StackTrace);
                    break;

                case DocumentCreationException _:
                    exception = new DocumentCreationException((int)HttpStatusCode.BadRequest, "Unable to create Document. Please try again later.", context.Exception.StackTrace);
                    break;

                case SRNUpdateNotAllowedException _:
                    exception = new SRNUpdateNotAllowedException((int)HttpStatusCode.BadRequest, "SRN changes not allowed for this user.", context.Exception.StackTrace);
                    break;

                case SRNActivityNotAllowedException _:
                    exception = new SRNActivityNotAllowedException((int)HttpStatusCode.BadRequest, exceptionMessage, context.Exception.StackTrace);
                    break;
                    
                case DocumentUpdateException _:
                    exception = new DocumentUpdateException((int)HttpStatusCode.BadRequest, "Unable to update Document. Please try again later.", context.Exception.StackTrace);
                    break;

                case DocumentDeleteException _:
                    exception = new DocumentDeleteException((int)HttpStatusCode.BadRequest, "Unable to delete Document. Please try again later.", context.Exception.StackTrace);
                    break;

                case ReplacementFileScheduleException _:
                    exception = new ReplacementFileScheduleException((int)HttpStatusCode.BadRequest, "An error ocurred while fetching replacement file schedule.", context.Exception.StackTrace);
                    break;

                case LookupGetSRNNumbersException _:
                    exception = new LookupGetSRNNumbersException((int)HttpStatusCode.BadRequest, "Unable to get SRN numbers for current user.", context.Exception.StackTrace);
                    break;

                case LookupGetSPNumbersException _:
                    exception = new LookupGetSPNumbersException((int)HttpStatusCode.BadRequest, "Unable to get SP numbers for current user.", context.Exception.StackTrace);
                    break;

                case SRNDateChangesNotAllowedException _:
                    exception = new SRNDateChangesNotAllowedException((int)HttpStatusCode.BadRequest, "SRN date changes not allowed. All other fields were updated", context.Exception.StackTrace);
                    break;

                case DataWarehouseException _:
                    exception = new DataWarehouseException((int)HttpStatusCode.BadRequest, exceptionMessage, context.Exception.StackTrace);
                    break;

                case RegistrationNumberExistsException _:
                    exception = new RegistrationNumberExistsException((int)HttpStatusCode.BadRequest, exceptionMessage, context.Exception.StackTrace);
                    break;

                case MemberSrnsActive:
                    exception = new MemberSrnsActive((int)HttpStatusCode.Conflict, "Member has active SRNs", context.Exception.StackTrace);
                    break;

                case Exception _:
                    if (context.Exception.Message.Contains("Error: Password"))
                    {
                        exception = new PasswordPolicyException((int)HttpStatusCode.BadRequest, context.Exception.Message, context.Exception.StackTrace);
                    }
                    else
                    {
                        exception = new CustomApiException((int)HttpStatusCode.InternalServerError, "An unknown error occured.", context.Exception.StackTrace);
                    }
                    break;
               
                default:
                    exception = new CustomApiException((int)HttpStatusCode.InternalServerError, "An unknown error occured.", context.Exception.StackTrace);
                    break;
            }

            detachAllEntities(_dbContext);

            var errorModel = new ApiError();

            //Do not log user credentials to the DB
            if (context.Exception is InvalidUserException)
                errorModel.Input = null;
            else if (context.Exception is EmailNotConfirmedException)
                errorModel.Input = null;

            try
            {
                _dbContext.ApiErrors.Add(errorModel);
                _dbContext.SaveChanges();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "Failed to write error to database");
            }

            var connectionID = context.HttpContext.TraceIdentifier;
            if (exception.StatusCode == 500)
            {
                Log.Error(context.Exception, $"[{connectionID}] Unhandled Exception ID: {errorModel.Id} Status Code: {exception.StatusCode} Exception Message: {exceptionMessage}");
            }
            else
            {
                Log.Warning(context.Exception, $"[{connectionID}] Handled Exception ID: {errorModel.Id} Status Code: {exception.StatusCode} Exception Message: {exceptionMessage}");
            }

            ApiErrorResource errorResource = new()
            {
                Id = errorModel.Id,
                Message = exception.ExMessage,
                StatusCode = exception.StatusCode
            };
            context.Result = new JsonResult(errorResource);
            context.HttpContext.Response.StatusCode = exception.StatusCode;

            base.OnException(context);
        }

        private void detachAllEntities(AppDbContext context)
        {
            var changedEntriesCopy = context.ChangeTracker.Entries()
                .Where(e => e.State == EntityState.Added ||
                            e.State == EntityState.Modified ||
                            e.State == EntityState.Deleted)
                .ToList();

            foreach (var entry in changedEntriesCopy)
                entry.State = EntityState.Detached;
        }
    }
}
