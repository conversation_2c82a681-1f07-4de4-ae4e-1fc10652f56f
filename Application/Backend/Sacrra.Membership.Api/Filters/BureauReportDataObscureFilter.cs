using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Database;
using Sacrra.Membership.Reporting.Models.ReportCommon;
using System;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using Sacrra.Membership.Reporting.Models;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Api.Helpers;

namespace Sacrra.Membership.Api.Filters
{
    /// <summary>
    /// Sacrra has a requirement that all bureau report data need to be obscured so other bureaus cannot know which bureau the data is from
    /// </summary>
    public class BureauReportDataObscureFilter : ActionFilterAttribute
    {
        private static string[] CONTROLLERS_TO_FILTER = new string[] {
                "DailyLoadReportController",
                "DailyLoadQEReportController",
                "DailySrnReportController",
                "MonthlyLoadReportController",
                "MonthlyLoadQEReportController",
                "MonthlyQeReportController",
                "MonthlySrnReportController",
                "ReportCommonController",
                "MonthlyOutOfSLAReportController",
                "DailyOutOfSLAReportController"
        };

        private static Dictionary<string, string> BUREAU_OBSCURE_MAPPING = new Dictionary<string, string>();

        // Create a reverse lookup for reversing the parameters provided from the Report Dashboard
        private static Dictionary<string, string> BUREAU_UNOBSCURE_MAPPING = new Dictionary<string, string>();

        static BureauReportDataObscureFilter()
        {
            BUREAU_OBSCURE_MAPPING = AppDbContextHelper.GetBureauObscureMappings();
            BUREAU_UNOBSCURE_MAPPING = BUREAU_OBSCURE_MAPPING.ToDictionary(x => x.Value, x => x.Key);
        }

        public override void OnActionExecuting(ActionExecutingContext context)
        {
            var actionDescriptor = context.ActionDescriptor as ControllerActionDescriptor;
            var controllerName = actionDescriptor.ControllerTypeInfo.Name;

            // If this isn't one of the reporting controllers carry on as usual
            // Note that this isn't the best way to do this but... the recommended way with a filter on the controllers didn't work
            if (!CONTROLLERS_TO_FILTER.Contains(controllerName))
            {
                base.OnActionExecuting(context);
                return;
            }

            // Make sure user is a Bureau
            var user = context.HttpContext.User;
            var claims = user.Claims.FirstOrDefault(c => c.Type.EndsWith("/claims/roles") && c.Value == "Bureau");
            if (claims == null)
            {
                base.OnActionExecuting(context);
                return;
            }

            // That is we want to check if the member that was logged is actually requesting MemberName data that it is allowed to see...
            if (context.ActionArguments.ContainsKey("parameters"))
            {
                var parameters = context.ActionArguments["parameters"] as RejectionReportParameters;

                // Unobscure bureau parameters coming from the UI
                for (int i=0; i<parameters.BureauName.Length; i++)
                {
                    if (BUREAU_UNOBSCURE_MAPPING.ContainsKey(parameters.BureauName[i]))
                    {
                        parameters.BureauName[i] = BUREAU_UNOBSCURE_MAPPING[parameters.BureauName[i]];
                    }
                }

            }

            base.OnActionExecuting(context);
        }

        public override void OnResultExecuting(ResultExecutingContext context)
        {            
            var actionDescriptor = context.ActionDescriptor as ControllerActionDescriptor;
            var controllerName = actionDescriptor.ControllerTypeInfo.Name;
      
            // If this isn't one of the reporting controllers carry on as usual
            if (!CONTROLLERS_TO_FILTER.Contains(controllerName))
            {
                base.OnResultExecuting(context);
                return;
            }

            // Make sure user is a Bureau
            var user = context.HttpContext.User;
            var claims = user.Claims.FirstOrDefault(c => c.Type.EndsWith("/claims/roles") && c.Value == "Bureau");
            if (claims == null)
            {
                base.OnResultExecuting(context);
                return;
            }

            // Get the RegisteredName (in our case the Bureau name) for the currently logged in Bureau
            var dbContext = context.HttpContext.RequestServices.GetService<AppDbContext>();
            string auth0UserId = Util.UserUtil.GetAuth0UserName(user);
            var dbUser = dbContext.Users.First(f => f.Auth0Id == auth0UserId);
            var currentUserName = dbContext.MemberUsers
                .Include(x => x.Member)
                .Where(u => u.UserId == dbUser.Id && u.Member.MembershipTypeId == MembershipTypes.Bureau)
                .Select(m => m.Member.RegisteredName.ToUpper())
                .FirstOrDefault() ?? "";

            var resultToReturn = (context.Result as ObjectResult).Value;

            if (resultToReturn is string[])
            {
                var resultStringArray = resultToReturn as string[];
                for (int i = 0; i < resultStringArray.Length; i++)
                {
                    resultStringArray[i] = obscureData(resultStringArray[i], currentUserName);
                }
            }

            // Obsure the beuru names in the KPI cards
            if (resultToReturn is CardCollectionModel)
            {
                var cardModel = (CardCollectionModel)resultToReturn;
                foreach (var childCard in cardModel.ChildCardCollection)
                {
                    childCard.Title = obscureData(childCard.Title, currentUserName);
                }
            }

            // Line chart obsure bureau
            if (resultToReturn is LineChart)
            {
                var lineChartModel = (LineChart)resultToReturn;
                foreach (var chartDataRow in lineChartModel.ChartData)
                {
                    chartDataRow.label = obscureData(chartDataRow.label, currentUserName);
                }
            }

            // Bar chart obsure bureau
            if (resultToReturn is BarChart)
            {
                var barChartModel = (BarChart)resultToReturn;
                for (int i = 0; i < barChartModel.ChartLabels.Count; i++)
                {
                    barChartModel.ChartLabels[i] = obscureData(barChartModel.ChartLabels[i], currentUserName);
                }

                foreach (var chartDataRow in barChartModel.ChartData)
                {
                    chartDataRow.label = obscureData(chartDataRow.label, currentUserName);
                }
            }

            // Obscure details grid
            if (resultToReturn is DetailsGridItem[])
            {
                var detailsResults = (DetailsGridItem[])resultToReturn;
                foreach (var rows in detailsResults)
                {
                    rows.BureauName = obscureData(rows.BureauName, currentUserName);
                }
            }

            base.OnResultExecuting(context);
        }

        private static string obscureData(string stringData, string currentUser)
        {
            if(stringData == null)
            {
                return null;
            }
            if (BUREAU_OBSCURE_MAPPING.ContainsKey(stringData) && stringData.ToUpper() != currentUser.ToUpper())
            {
                return BUREAU_OBSCURE_MAPPING[stringData];
            }

            return stringData;
        }
    }
}
