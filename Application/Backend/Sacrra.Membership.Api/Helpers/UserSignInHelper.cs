using Microsoft.IdentityModel.Tokens;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Resources.Auth;
using Sacrra.Membership.Database.Enums;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;

namespace Sacrra.Membership.Api.Helpers
{
    public static class UserSignInHelper
    {
        public static string GetTokenString(AuthUserResource signedInUser, string jwtKey)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = System.Text.Encoding.ASCII.GetBytes(jwtKey);
            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new Claim[]
                {
                    new Claim(ClaimTypes.NameIdentifier, signedInUser.Id.ToString()),
                    new Claim(ClaimTypes.Name, signedInUser.Email),
                }),
                Expires = DateTime.Now.AddDays(1),
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key),
                SecurityAlgorithms.HmacSha256Signature)
            };

            var userRole = EnumHelper.GetEnumIdValuePair<UserRoles>(signedInUser.RoleId);

            tokenDescriptor.Subject.AddClaim(new Claim(ClaimTypes.Role, userRole.Value));

            var token = tokenHandler.CreateToken(tokenDescriptor);
            var tokenString = tokenHandler.WriteToken(token);

            return tokenString;
        }
    }
}
