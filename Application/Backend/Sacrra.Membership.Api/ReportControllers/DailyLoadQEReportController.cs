using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Reporting.Models;
using Sacrra.Membership.Reporting.Models.ReportCommon;
using Sacrra.Membership.Reporting.Services;

namespace Sacrra.Membership.Api.ReportControllers
{
    [Authorize(Roles = "Financial Administrator,Member,Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator,ALG Leader,Bureau")]
    [ApiController]
    [Route("api/v{v:apiVersion}/[controller]/[action]")]
    public class DailyLoadQEReportController : Controller
    {
        private DailyLoadQEReportService _dailySrnReportService;
        public DailyLoadQEReportController(DailyLoadQEReportService dailySrnReportService)
        {
            _dailySrnReportService = dailySrnReportService;
        }

        [HttpPost]
        public ActionResult<CardCollectionModel> GetDailySrnExceptions(RejectionReportParameters parameters)
        {
            var result = _dailySrnReportService.GetDailySrnExceptions(parameters);
            return result;
        }

        [HttpPost]
        public ActionResult<SrnExceptionDetails[]> GetDailySrnExceptionDetails(RejectionReportParameters parameters)
        {
            var result = _dailySrnReportService.GetDailySrnExceptionDetails(parameters);
            return result;
        }

        [HttpPost]
        public ActionResult<QEItem[]> GetQEList()
        {
            var result = _dailySrnReportService.GetQEList();
            return result;
        }
    }
}
