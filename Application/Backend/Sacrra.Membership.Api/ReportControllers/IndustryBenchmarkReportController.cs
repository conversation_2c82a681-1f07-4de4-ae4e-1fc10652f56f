using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Business.DTOs;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Reporting.Models;
using Sacrra.Membership.Reporting.Services;
using System.Collections.Generic;

namespace Sacrra.Membership.Api.ReportControllers
{
    [Authorize(Roles = "Financial Administrator,Member,Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator,ALG Leader,Bureau")]
    [ApiController]
    [ApiVersion("2")]
    [Route("api/v{v:apiVersion}/[controller]/[action]")]
    public class IndustryBenchmarkReportController : Controller
    {
        private IndustryBenchmarkReportService _industryMatricsService;
        private readonly GlobalHelper _globalHelper;
        public IndustryBenchmarkReportController(IndustryBenchmarkReportService industryMatricsService, GlobalHelper globalHelper)
        {
            _industryMatricsService = industryMatricsService;
            _globalHelper = globalHelper;
        }

        [HttpGet]
        [ProducesResponseType(type: typeof(IndustryBenchmarkReportOutputFilterDTO), statusCode: 200)]
        [ProducesResponseType(typeof(void), 204)]
        [ProducesResponseType(typeof(void), 400)]
        public ActionResult<IndustryBenchmarkReportOutputFilterDTO> GetFilters()
        {
            var user = _globalHelper.GetUserByAuth0Id(User.Identity.Name);

            var result = _industryMatricsService.GetFilters(user);
            return result;
        }

        [HttpPost]
        public ActionResult<List<IndustryBenchmarkBureauCardDTO>> GetIndustryBenchmarkReportCards([FromBody] IndustryBenchmarkInputReportInputFilterDTO filter)
        {
            var result = _industryMatricsService.GetIndustryBenchmarkReportCards(filter);
            return result;
        }
        
        [HttpPost]
        public ActionResult<BarChart> GetIndustryRecordsVsMemberRecordsReceivedCharts([FromBody] IndustryBenchmarkInputReportInputFilterDTO filter)
        {
            var result = _industryMatricsService.GetIndustryRecordsReceivedVsMemberRecordsReceivedCharts(filter);
            return result;
        }

        [HttpPost]
        public ActionResult<BarChart> GetIndustryRecordsRejectedVsMemberRecordsRejectedCharts([FromBody] IndustryBenchmarkInputReportInputFilterDTO filter)
        {
            var result = _industryMatricsService.GetIndustryRecordsRejectedVsMemberRecordsRejectedCharts(filter);
            return result;
        }
    }
}
