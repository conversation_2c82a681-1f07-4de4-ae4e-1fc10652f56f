using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Reporting.Models;
using Sacrra.Membership.Reporting.Models.ReportCommon;
using Sacrra.Membership.Reporting.Services;

namespace Sacrra.Membership.Api.ReportControllers
{
    [Authorize(Roles = "Financial Administrator,Member,Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator,ALG Leader,Bureau")]
    [ApiController]
    [Route("api/v{v:apiVersion}/[controller]/[action]")]
    public class MonthlyLoadQEReportController : Controller
    {
        private MonthlyLoadQEReportService _monthlySrnReportService;
        public MonthlyLoadQEReportController(MonthlyLoadQEReportService monthlySrnReportService)
        {
            _monthlySrnReportService = monthlySrnReportService;
        }

        [HttpPost]
        public ActionResult<CardCollectionModel> GetMonthlySrnExceptions(RejectionReportParameters parameters)
        {
            var result = _monthlySrnReportService.GetMonthlySrnExceptions(parameters);
            return result;
        }

        [HttpPost]
        public ActionResult<SrnExceptionDetails[]> GetMonthlySrnExceptionDetails(RejectionReportParameters parameters)
        {
            var result = _monthlySrnReportService.GetMonthlySrnExceptionDetails(parameters);
            return result;
        }

        [HttpPost]
        public ActionResult<QEItem[]> GetQEList()
        {
            var result = _monthlySrnReportService.GetQEList();
            return result;
        }
    }
}
