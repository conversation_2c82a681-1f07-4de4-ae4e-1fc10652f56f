using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sacrra.Membership.Reporting.Models;
using Sacrra.Membership.Reporting.Models.OutOfSLAReportModels;
using Sacrra.Membership.Reporting.Models.ReportCommon;
using Sacrra.Membership.Reporting.Services;

namespace Sacrra.Membership.Api.ReportControllers
{
    [Authorize(Roles = "Financial Administrator,Member,Stakeholder Manager,Group Stakeholder Manager,SACRRA Administrator,ALG Leader,Bureau")]
    [ApiController]
    [Route("api/v{v:apiVersion}/[controller]/[action]")]
    public class MonthlyOutOfSLAReportController : Controller
    {
        private MonthlyOutOfSLAService _monthlyOutOfSLAService;

        public MonthlyOutOfSLAReportController(MonthlyOutOfSLAService monthlyOutOfSLAService)
        {
            _monthlyOutOfSLAService = monthlyOutOfSLAService;
        }

        [HttpPost]
        public ActionResult<CardCollectionModel> GetMemberSubmissionAnalysisKpiCards([FromBody] RejectionReportParameters parameters)
        {
            var result = _monthlyOutOfSLAService.GetMemberSubmissionAnalysisKpiCards(parameters);
            return result;
        }

        [HttpPost]
        public ActionResult<CardCollectionModel> GetMemberBureauAnalysisKpiCards([FromBody] RejectionReportParameters parameters)
        {
            var result = _monthlyOutOfSLAService.GetMemberBureauAnalysisKpiCards(parameters);
            return result;
        }

        [HttpPost]
        public ActionResult<BarChart> GetMemberSrnNonSubmissionVsOutOfSlaChart([FromBody] RejectionReportParameters parameters)
        {
            var result = _monthlyOutOfSLAService.GetMemberSrnNonSubmissionVsOutOfSlaChart(parameters);
            return result;
        }

        [HttpPost]
        public ActionResult<BarChart> GetMemberSrnsOutOfSlaPerBureauChart([FromBody] RejectionReportParameters parameters)
        {
            var result = _monthlyOutOfSLAService.GetMemberSrnsOutOfSlaPerBureauChart(parameters);
            return result;
        }

        [HttpPost]
        public ActionResult<BarChart> GetMemberSrnsNotLoadedPerBureauChart([FromBody] RejectionReportParameters parameters)
        {
            var result = _monthlyOutOfSLAService.GetMemberSrnsNotLoadedPerBureauChart(parameters);
            return result;
        }

        [HttpPost]
        public ActionResult<CardCollectionModel> GetSrnSubmissionAnalysisCards([FromBody] RejectionReportParameters parameters)
        {
            var result = _monthlyOutOfSLAService.GetSrnSubmissionAnalysisCards(parameters);
            return result;
        }

        [HttpPost]
        public ActionResult<CardCollectionModel> GetSrnBureauAnalysisCards([FromBody] RejectionReportParameters parameters)
        {
            var result = _monthlyOutOfSLAService.GetSrnBureauAnalysisCards(parameters);
            return result;
        }

        [HttpPost]
        public ActionResult<OutOfSlaItem[]> GetDetails([FromBody] RejectionReportParameters parameters, string dimDTHReportType = null)
        {
            var result = _monthlyOutOfSLAService.GetDetails(parameters, dimDTHReportType);
            return result;
        }
    }
}
