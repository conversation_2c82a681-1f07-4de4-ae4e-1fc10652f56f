using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Business.DTOs.DataWarehouseDTO;
using Sacrra.Membership.Reporting.Helper;
using Sacrra.Membership.Reporting.Models.FileSubmissionModels;
using Sacrra.Membership.Reporting.Models.ReportCommon;
using Sacrra.Membership.Reporting.Services;

namespace Sacrra.Membership.Api.ReportControllers
{
    [Authorize(Roles = "Member, Stakeholder Manager, Bureau, ALG Leader")]
    [ApiController]
    [Route("api/v{v:apiVersion}/[controller]/[action]")]
    public class ReplacementFileSubmissionController : Controller
    {
        private FileReplacementSubmissionService _fileReplacementSubmissionService;

        public ReplacementFileSubmissionController(FileReplacementSubmissionService fileReplacementSubmissionService)
        {
            _fileReplacementSubmissionService = fileReplacementSubmissionService;
        }

        [HttpPost]
        public ActionResult<FileSubmissionOutputDTO[]> GetFileSubmissions([FromBody] FileSubmissionInputDTO inputDTO)
        {
            return _fileReplacementSubmissionService.GetFileSubmissions(inputDTO);
        }
    }
}
