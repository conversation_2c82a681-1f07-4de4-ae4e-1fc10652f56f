using Sacrra.Membership.Database.Enums;
using System;
using System.Linq;
using System.Security.Claims;

namespace Sacrra.Membership.Api.Util
{
    public class UserUtil
    {
        internal static UserRoles GetUserRole(ClaimsPrincipal user)
        {
            // default role is one with no rights
            var result = UserRoles.User;

            if (user == null)
                return result;
            if (user.Claims.Count() == 0)
                return result;

            var roleClaim = user.Claims.FirstOrDefault(t => t.Type.Contains("claims/roles"));

            if (roleClaim == null)
                return result;

            Enum.TryParse(roleClaim.Value, true, out result);
            return result;
        }

        internal static string GetAuth0UserName(ClaimsPrincipal user)
        {
            return user.Identity?.Name;
        }
    }
}
