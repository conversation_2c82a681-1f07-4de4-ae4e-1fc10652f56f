using System.Collections.Generic;
using System.Threading.Tasks;
using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Database.Enums;

namespace Sacrra.Membership.Camunda.CamundaAgents;

public class NewMemberTakeonCamundaAgent: BaseCamundaAgent
{
    public NewMemberTakeonCamundaAgent(CamundaClient camundaClient) : base(camundaClient) { }

    public override async Task Process(string topicName, ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        switch (topicName)
        {
            case "disqualify-member-and-email":
                await DisqualifyMemberAndEmail(task, completeExternalTask, serviceScope);
                break;
            
            case "allocate-stake-holder-administrator":
                await AllocateStakeHolderAdministrator(task, completeExternalTask, serviceScope);
                break;
            
            case "member-approve":
                await <PERSON><PERSON><PERSON><PERSON>(task, completeExternalTask, serviceScope);
                break;
            
            case "notify-member-of-application-approval":
                await NotifyMemberOfApplicationApproval(task, completeExternalTask, serviceScope);
                break;
            
            case "new-membership-update-member-status":
                await NewMembershipUpdateMemberStatus(task, completeExternalTask, serviceScope);
                break;
            
            case "notify-ALG-leader-on-client-takeon":
                await NotifyALGLeaderOnClientTakeon(task, completeExternalTask, serviceScope);
                break;
            
            case "send-member-assessment-payment-reminder":
            case "send-member-application-cancellation-email":
            case "send-payment-and-constitution-reminder":
            case "send-member-onboarding-payment-reminder":
                break;
        }
            
        await task.Complete(completeExternalTask);
    }

    private async Task NotifyALGLeaderOnClientTakeon(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var memberId = await GetMemberVariable(task, "OrganisationID", serviceScope);

        await GetNewMemberTakeonCamundaService(serviceScope).NotifyALGLeaderOnClientRegistration(memberId);
    }

    private async Task NewMembershipUpdateMemberStatus(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var memberId = await GetMemberVariable(task, "OrganisationID", serviceScope);

        await GetNewMemberTakeonCamundaService(serviceScope).UpdateMemberStatus(ApplicationStatuses.MemberRegistrationCompleted, memberId);
    }

    private async Task NotifyMemberOfApplicationApproval(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var memberId = await GetMemberVariable(task, "OrganisationID", serviceScope);

        await GetNewMemberTakeonCamundaService(serviceScope).NotifyMemberOfApplicationApproval(memberId);
    }

    private async Task MemberApprove(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var memberId = await GetMemberVariable(task, "OrganisationID", serviceScope);

        await GetNewMemberTakeonCamundaService(serviceScope).UpdateMemberStatus(ApplicationStatuses.MemberRegistrationApproved, memberId);
        await GetNewMemberTakeonCamundaService(serviceScope).NotifyMemberOfApplicationApproval(memberId);
    }

    private async Task AllocateStakeHolderAdministrator(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var randomUser = await GetRandomUser("StakeHolderAdministrator");

        completeExternalTask.Variables = new Dictionary<string, VariableValue>
        {
            ["stakeHolderManagerManagerAssignee"] = VariableValue.FromObject(randomUser.Id)
        };
    }

    private async Task DisqualifyMemberAndEmail(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var memberId = await GetMemberVariable(task, "OrganisationID", serviceScope);

        await GetNewMemberTakeonCamundaService(serviceScope).NotifyMemberOfApplicationDisqualification(memberId);
    }
    
    private static NewMemberTakeonCamundaService GetNewMemberTakeonCamundaService(IServiceScope serviceScope)
    {
        return serviceScope.ServiceProvider.GetRequiredService<NewMemberTakeonCamundaService>();
    }
}