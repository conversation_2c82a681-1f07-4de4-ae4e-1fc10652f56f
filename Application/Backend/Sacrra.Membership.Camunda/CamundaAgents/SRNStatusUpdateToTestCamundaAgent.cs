using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Camunda.Api.Client;
using Camunda.Api.Client.ExternalTask;
using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Business.Services;

namespace Sacrra.Membership.Camunda.CamundaAgents;

public class SrnStatusUpdateToTestCamundaAgent: BaseCamundaAgent
{
    public SrnStatusUpdateToTestCamundaAgent(CamundaClient camundaClient) : base(camundaClient)
    {
    }

    public override async Task Process(string topicName, ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        switch (topicName)
        {
            case "email-bureaus-on-srn-go-live":
                await EmailBureausOnSrnGoLive(task, completeExternalTask, serviceScope);
                break;
            
            case "email-bureaus-after-srn-testing-srn-status-update":
                await EmailBureausAfterSrnTestingSrnStatusUpdate(task, completeExternalTask, serviceScope);
                break;
            
            case "update-srn-and-file-statuses":
                await UpdateSrnAndFileStatuses(task, completeExternalTask, serviceScope);
                break;
            
            case "update-srn-status-to-test":
                await UpdateSrnStatusToTest(task, completeExternalTask, serviceScope);
                break;
        }

        await task.Complete(completeExternalTask);
    }

    private async Task UpdateSrnStatusToTest(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var isLiveFileSubmissionsSuspended = await GetGenericVariable(task, "isLiveFileSubmissionsSuspended", serviceScope);

        if (isLiveFileSubmissionsSuspended == "true")
        {
            var srnId = await GetGenericVariable(task, "SRNId", serviceScope);
            var srnStatusName = await GetGenericVariable(task, "SRNStatusName", serviceScope);
            var updatedByUserIdVariable = await GetGenericVariable(task, "UpdatedByUserId", serviceScope);

            if (!string.IsNullOrEmpty(srnId) && !string.IsNullOrEmpty(srnStatusName))
            {
                var updatedByUserId = (!string.IsNullOrEmpty(updatedByUserIdVariable)) ? Convert.ToInt32(updatedByUserIdVariable) : 0;
                var taskInfo = await task.Get();
                var processInstanceId = taskInfo.ProcessInstanceId;

                await GetSrnStatusUpdateToTestCamundaService(serviceScope).UpdateSRNStatus(Convert.ToInt32(srnId), srnStatusName, updatedByUserId, processInstanceId);
                await GetSrnStatusUpdateToTestCamundaService(serviceScope).UpdateRolloutStatus("Test", processInstanceId);
            }
        }
    }

    private async Task UpdateSrnAndFileStatuses(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = await GetIntegerVariable(task, "SRNId", serviceScope);
        var taskInfo = await task.Get();
        var processInstanceId = string.Empty;
        var updatedByUserIdVariable = await GetIntegerVariable(task, "UpdatedByUserId", serviceScope);
        
        if (taskInfo != null)
        {
            processInstanceId = taskInfo.ProcessInstanceId;
        }

        await GetSrnStatusUpdateToTestCamundaService(serviceScope).UpdateSrnAndFileStatuses(srnId, processInstanceId, updatedByUserIdVariable, task);
        await GetSrnStatusUpdateToTestCamundaService(serviceScope).UpdateRolloutStatus("Test", processInstanceId);
    }

    private async Task EmailBureausAfterSrnTestingSrnStatusUpdate(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = await GetIntegerVariable(task, "SRNId", serviceScope);

        await GetSrnStatusUpdateToTestCamundaService(serviceScope).EmailBureausAfterSRNTesting(srnId, "SRN Status Update");
    }

    private async Task EmailBureausOnSrnGoLive(ExternalTaskResource task, CompleteExternalTask completeExternalTask, IServiceScope serviceScope)
    {
        var srnId = await GetIntegerVariable(task, "SRNId", serviceScope);
        var randomUser = await GetRandomUser("SACRRAAdministrator");
        
        await GetSrnStatusUpdateToTestCamundaService(serviceScope).EmailBureausOnSRNGoLive(srnId);
        completeExternalTask.Variables = new Dictionary<string, VariableValue>
        {
            ["SACRRAAdminAssignee"] = VariableValue.FromObject(randomUser.Id)
        };
    }
    
    private static SrnStatusUpdateToTestCamundaService GetSrnStatusUpdateToTestCamundaService(IServiceScope serviceScope)
    {
        return serviceScope.ServiceProvider.GetRequiredService<SrnStatusUpdateToTestCamundaService>();
    }
}