using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Repositories;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Notification.Repositories;

namespace Sacrra.Membership.Business.Services;

public class NewMemberTakeonCamundaService
{
    private readonly AppDbContext _dbContext;
    private readonly EmailService _emailService;
    private readonly AuthRepository _authRepository;
    private readonly UserRepository _userRepository;

    public NewMemberTakeonCamundaService(AppDbContext dbContext, EmailService emailService,
        AuthRepository authRepository, UserRepository userRepository)
    {
        _dbContext = dbContext;
        _emailService = emailService;
        _authRepository = authRepository;
        _userRepository = userRepository;
    }

    public async Task NotifyALGLeaderOnClientRegistration(int memberId)
    {
        try
        {
            var clientLeader = await _dbContext.ALGClientLeaders
                .Include(i => i.Client)
                .Include(i => i.Leader)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.ClientId == memberId);

            if (clientLeader != null)
            {
                if (clientLeader.Client != null)
                {
                    var client = await _dbContext.Set<Member>()
                        .FirstOrDefaultAsync(i => i.Id == clientLeader.Client.Id);

                    if (client != null)
                    {
                        client.DateActivated = DateTime.Now;
                    }

                    await _dbContext.SaveChangesAsync();
                }

                if (clientLeader.Leader != null)
                {
                    var mainContact = await Helpers.Helpers.GetMemberMainContact(_dbContext, clientLeader.Leader.Id);

                    if (mainContact != null)
                    {
                        var leader = mainContact;
                        var placeholders = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("[ALGClient]", clientLeader.Client.RegisteredName)
                        };

                        _emailService.SendEmail(leader.Email, leader.FirstName, "ALG Client Registration",
                            "MemberNewALGClientRegistrationALGLeader.html", placeholders);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to email ALG leader about client registration " + memberId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }

    private async Task CreateMemberStatusUpdateEventLog(Member member, MemberStagingChangeLogResource stagingChangeLog,
        bool isSystemUser = false)
    {
        var updateDetailsBlob = JsonConvert.SerializeObject(member,
            new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore });

        var stagingDetailsBlob = JsonConvert.SerializeObject(stagingChangeLog);

        if (stagingChangeLog.Changes.Count > 0)
        {
            var userId = 0;
            var user = await Helpers.Helpers.GetUserByAuth0Id(_dbContext, isSystemUser);
            if (user != null)
            {
                userId = user.Id;
            }

            await Helpers.Helpers.CreateEventLog(_dbContext, userId, "Member Update", member.RegisteredName, updateDetailsBlob,
                stagingDetailsBlob, member.Id, "Member");
        }
    }

    public async Task UpdateUserRoleToMember(int memberId)
    {
        try
        {
            var member = await _dbContext.Members
                .Include(i => i.Users)
                .ThenInclude(x => x.User)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == memberId);

            if (member != null)
            {
                if (member.Users != null)
                {
                    foreach (var memberUser in member.Users)
                    {
                        if (memberUser.User.RoleId != UserRoles.FinancialAdministrator
                            && memberUser.User.RoleId != UserRoles.SACRRAAdministrator
                            && memberUser.User.RoleId != UserRoles.StakeHolderAdministrator
                            && memberUser.User.RoleId != UserRoles.StakeHolderManager)
                        {
                            var user = memberUser.User;
                            user.RoleId = UserRoles.Member;

                            Helpers.Helpers.PrepareUserForUpdate(_dbContext, user);
                            _dbContext.Set<User>().Update(user);
                            await _dbContext.SaveChangesAsync();

                            var memberRoleId = await _authRepository.GetAuth0RoleIDByName("Member");
                            await _authRepository.AssignAuth0UserRole(user.Auth0Id, memberRoleId);

                            var userRoleId = await _authRepository.GetAuth0RoleIDByName("User");
                            await _userRepository.RemoveAuth0RoleFromUser(user.Auth0Id, userRoleId);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to user role to Member. Member Id " + memberId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }

    public async Task UpdateMemberStatus(ApplicationStatuses newStatus, int memberId)
    {
        try
        {
            var member = _dbContext.Members.Find(memberId);

            var oldMemberStatus = member.ApplicationStatusId;

            if (member != null)
            {
                member.ApplicationStatusId = newStatus;

                if (newStatus == ApplicationStatuses.MemberRegistrationCompleted)
                {
                    member.DateActivated = DateTime.Now;
                }

                _dbContext.SaveChanges();

                MemberStagingChangeLogResource stagingChangeLog = new();

                stagingChangeLog.Changes.Add(new StagingChange
                {
                    Name = "Application Status",
                    OldValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)oldMemberStatus).Value,
                    NewValue = EnumHelper.GetEnumIdValuePair<ApplicationStatuses>((int)member.ApplicationStatusId).Value
                });

                await CreateMemberStatusUpdateEventLog(member, stagingChangeLog, isSystemUser: true);
                await UpdateUserRoleToMember(memberId);
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to update Member (status) with id " + memberId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }

    public async Task NotifyMemberOfApplicationApproval(int memberId)
    {
        try
        {
            var member = await _dbContext.Members
                .Include(i => i.StakeholderManager)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == memberId);

            if (member != null)
            {
                var mainContact = await Helpers.Helpers.GetMemberMainContact(_dbContext, memberId);

                if (mainContact != null)
                {
                    var applicant = mainContact;
                    var message = member.RegisteredName;
                    var shm = (member.StakeholderManager != null)
                        ? member.StakeholderManager.FirstName + " " + member.StakeholderManager.LastName
                        : "";

                    List<KeyValuePair<string, string>> placeholders = new List<KeyValuePair<string, string>>();

                    placeholders.Add(new KeyValuePair<string, string>("[MemberRegisteredName]", member.RegisteredName));
                    placeholders.Add(new KeyValuePair<string, string>("[StakeholderManager]", shm));

                    _emailService.SendEmail(applicant.Email, applicant.FirstName, "Membership Application Successful",
                        "MembershipApplicationSuccessfulApplicant.html", placeholders);
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to email applicant for membership application successful. Member Id " + memberId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }

    public async Task NotifyMemberOfApplicationDisqualification(int memberId)
    {
        try
        {
            var member = await _dbContext.Members
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == memberId);
            var disqualifiedReason = "";
            

            if (member != null)
            {
                if(member.DisqualifiedReasonMappingId != null)
                {

                    var disqualifiedReasonMapping = _dbContext.DisqualifiedReasonMapping
                        .Where(s => s.Id == member.DisqualifiedReasonMappingId)
                        .FirstOrDefault();
                    
                    if(disqualifiedReasonMapping != null)
                    {
                        var freeTextReason = disqualifiedReasonMapping.FreeTextReason;
                        var reasonId = disqualifiedReasonMapping.ReasonId;

                        var reason = _dbContext.DisqualifiedReasonTypes.Where(s => s.Id == reasonId).Select(s => s.Name).FirstOrDefault();
                        disqualifiedReason = reason + " - " + freeTextReason;
                    }
                }
                var mainContact = await Helpers.Helpers.GetMemberMainContact(_dbContext, memberId);

                if (member.MembershipTypeId == MembershipTypes.ALGClient)
                {
                    var leader = await _dbContext.ALGClientLeaders
                        .Include(i => i.Leader)
                        .AsNoTracking()
                        .FirstOrDefaultAsync(i => i.ClientId == member.Id);

                    if (leader != null)
                    {
                        if (leader.Leader != null)
                        {
                            var leaderMainContact = await Helpers.Helpers.GetMemberMainContact(_dbContext, leader.Leader.Id);

                            if (leaderMainContact != null)
                            {
                                var applicant = leaderMainContact;
                                var placeholders = new List<KeyValuePair<string, string>>
                                {
                                    new KeyValuePair<string, string>("[MemberRegisteredName]", member.RegisteredName),
                                    new KeyValuePair<string, string>("[ReasonDisqualified]",disqualifiedReason)
                                };

                                _emailService.SendEmail(applicant.Email, applicant.FirstName,
                                    "ALG Client Membership Application Disqualified",
                                    "ALGClientMemberApplicationRejectedApplicantLeader.html", placeholders);
                            }
                        }
                    }
                }

                else if (mainContact != null)
                {
                    var applicant = mainContact;
                    var placeholders = new List<KeyValuePair<string, string>>
                    {
                        new KeyValuePair<string, string>("[MemberRegisteredName]", member.RegisteredName),
                        new KeyValuePair<string, string>("[ReasonDisqualified]",disqualifiedReason)
                    };

                    _emailService.SendEmail(applicant.Email, applicant.FirstName, "Membership Application Disqualified",
                        "MemberApplicationRejectedApplicant.html", placeholders);
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to email applicant for membership application disqualification. Member Id " +
                          memberId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }
}