using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.Repositories;
using Sacrra.Membership.Business.Resources.Camunda.Task;
using Sacrra.Membership.Business.Resources.MemberChangeRequest;
using Sacrra.Membership.Database;
using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.Models;
using Sacrra.Membership.Notification.Repositories;

namespace Sacrra.Membership.Business.Services;

public class SrnSellingCamundaService
{
    private readonly AppDbContext _dbContext;
    private readonly EmailService _emailService;
    private readonly SRNRepository _srnRepository;
    private readonly ConfigSettings _configSettings;

    public SrnSellingCamundaService(AppDbContext dbContext, EmailService emailService, SRNRepository srnRepository,
        IOptions<ConfigSettings> configSettings)
    {
        _dbContext = dbContext;
        _emailService = emailService;
        _srnRepository = srnRepository;
        _configSettings = configSettings.Value;
    }

    private SRNStatusUpdateHistory GetRecentSRNStatusUpdateHistory(ICollection<SRNStatusUpdateHistory> srnStatusUpdates,
        string fileType)
    {
        if (srnStatusUpdates != null)
        {
            if (srnStatusUpdates.Count > 0)
            {
                if (fileType != null)
                {
                    if (fileType == "MonthlyFile")
                        srnStatusUpdates = srnStatusUpdates.Where(i => i.FileType == SRNStatusFileTypes.MonthlyFile)
                            .ToList();
                    else if (fileType == "DailyFile")
                        srnStatusUpdates = srnStatusUpdates.Where(i => i.FileType == SRNStatusFileTypes.DailyFile)
                            .ToList();
                }

                if (srnStatusUpdates != null)
                {
                    if (srnStatusUpdates.Count > 0)
                    {
                        srnStatusUpdates = srnStatusUpdates.OrderByDescending(i => i.DateCreated).ToList();
                        return srnStatusUpdates.FirstOrDefault();
                    }
                }
            }
        }

        return new SRNStatusUpdateHistory();
    }

    public async Task EmailBureausOnSRNGoLive(int srnId)
    {
        try
        {
            var selectRecord = await _dbContext.SRNs
                .Include(i => i.Member)
                .Include(i => i.SPGroup)
                .Include(i => i.SRNStatusUpdates)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == srnId);

            string message = selectRecord.SRNNumber;

            if (selectRecord != null)
            {
                List<KeyValuePair<string, string>> placeholders = new List<KeyValuePair<string, string>>();

                placeholders.Add(new KeyValuePair<string, string>("[MemberRegisteredName]",
                    selectRecord.Member.RegisteredName));

                var tradingName = (selectRecord.TradingName != null) ? selectRecord.TradingName : "Not Specified";
                placeholders.Add(new KeyValuePair<string, string>("[SRNNumber]", selectRecord.SRNNumber));

                var dailySRNStatusUpdate = GetRecentSRNStatusUpdateHistory(selectRecord.SRNStatusUpdates, "DailyFile");
                var monthlySRNStatusUpdate =
                    GetRecentSRNStatusUpdateHistory(selectRecord.SRNStatusUpdates, "MonthlyFile");

                var dailyGoLiveDate = (dailySRNStatusUpdate.DailyFileGoLiveDate != null)
                    ? string.Format("{0:yyyy-MM-dd}", dailySRNStatusUpdate.DailyFileGoLiveDate.Value)
                    : "Not Specified";
                var monthlyGoLiveDate = (monthlySRNStatusUpdate.MonthlyFileGoLiveDate != null)
                    ? string.Format("{0:yyyy-MM-dd}", monthlySRNStatusUpdate.MonthlyFileGoLiveDate.Value)
                    : "Not Specified";

                placeholders.Add(new KeyValuePair<string, string>("[DailyGoLiveDate]", dailyGoLiveDate));
                placeholders.Add(new KeyValuePair<string, string>("[MonthlyGoLiveDate]", monthlyGoLiveDate));

                var bureaus = await _dbContext.Members
                    .Include(i => i.Contacts)
                    .AsNoTracking()
                    .Where(i => i.MembershipTypeId == MembershipTypes.Bureau && i.RegisteredName != "TRANSUNION")
                    .ToListAsync();

                var mainContactType = await _dbContext.ContactTypes
                    .AsNoTracking()
                    .FirstOrDefaultAsync(i => i.Name == "Data Contact Details");

                foreach (var entity in bureaus)
                {
                    if (mainContactType != null)
                    {
                        var mainContact = entity.Contacts
                            .FirstOrDefault(i => i.ContactTypeId == mainContactType.Id);

                        if (mainContact != null)
                        {
                            if (!string.IsNullOrEmpty(mainContact.Email))
                                _emailService.SendEmail(mainContact.Email, mainContact.FirstName, "SRN is Live",
                                    "EmailBureausSRNIsLive.html", placeholders);
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to email bureaus for SRN Id " + srnId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }

    public async Task UpdateSRNStatus(int srnId, string newStatus, int updatedByUserId = 0,
        string processInstanceId = null, bool isNewSRN = false)
    {
        await _srnRepository.UpdateSRNStatus(srnId, newStatus, updatedByUserId, processInstanceId, isNewSRN);
    }

    public async Task NotifySellerAndSHMOfSaleRequestCommencement(int srnId)
    {
        try
        {
            var srn = await _dbContext.SRNs
                .Include(i => i.Member)
                .ThenInclude(i => i.StakeholderManager)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == srnId);

            if (srn != null)
            {
                if (srn.Member != null)
                {
                    if (srn.Member.StakeholderManager != null)
                    {
                        var shm = srn.Member.StakeholderManager;
                        var placeholders = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("[SRNNumber]", srn.SRNNumber)
                        };
                        _emailService.SendEmail(shm.Email, shm.FirstName, "SRN Sale Request Commencement",
                            "SRNSaleRequestCommencementSellerSHM.html", placeholders);
                    }

                    var mainContact = await Helpers.Helpers.GetMemberMainContact(_dbContext, srn.Member.Id);

                    if (mainContact != null)
                    {
                        var seller = mainContact;
                        var placeholders = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("[SRNNumber]", srn.SRNNumber)
                        };

                        _emailService.SendEmail(seller.Email, seller.FirstName, "SRN Sale Request Commencement",
                            "SRNSaleRequestCommencementSellerMember.html", placeholders);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to email seller SHM and member for SRN sale commencement. SRN Id " + srnId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }

    public async Task UpdateSRNStatus(int srnId, int statusId)
    {
        await _srnRepository.UpdateSRNStatus(srnId, statusId);
    }

    public async Task NotifyMemberOfSRNSplitCancellation(int splitFromSRNId)
    {
        try
        {
            var srn = await _dbContext.SRNs
                .Include(i => i.Member)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == splitFromSRNId);

            if (srn != null)
            {
                if (srn.Member != null)
                {
                    var mainContact = await Helpers.Helpers.GetMemberMainContact(_dbContext, srn.Member.Id);

                    if (mainContact != null)
                    {
                        var applicant = mainContact;
                        var placeholders = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("[SRNNumber]", srn.SRNNumber)
                        };

                        _emailService.SendEmail(applicant.Email, applicant.FirstName, "SRN Split Request Rejected",
                            "SRNSplitRequestRejectedApplicant.html", placeholders);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to email applicant for SRN merge request. SRN Id " + splitFromSRNId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }

    public async Task NotifyMemberOfSRNMergeCancellation(int mergeToSRNId)
    {
        try
        {
            var srn = await _dbContext.SRNs
                .Include(i => i.Member)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == mergeToSRNId);

            if (srn != null)
            {
                if (srn.Member != null)
                {
                    var mainContact = await Helpers.Helpers.GetMemberMainContact(_dbContext, srn.Member.Id);

                    if (mainContact != null)
                    {
                        var applicant = mainContact;
                        var placeholders = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("[SRNNumber]", srn.SRNNumber)
                        };

                        _emailService.SendEmail(applicant.Email, applicant.FirstName, "SRN Merge Request Rejected",
                            "SRNMergeRequestRejectedApplicant.html", placeholders);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to email applicant for SRN merge request. SRN Id " + mergeToSRNId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }

    public async Task NotifyBureusAndSHMOfSaleFileSubmissionCompletion(int srnId)
    {
        try
        {
            var srn = await _dbContext.SRNs
                .Include(i => i.Member)
                .ThenInclude(i => i.StakeholderManager)
                .Include(i => i.Member)
                .ThenInclude(i => i.PrimaryBureau)
                .ThenInclude(i => i.Contacts)
                .Include(i => i.Member)
                .ThenInclude(i => i.SecondaryBureau)
                .ThenInclude(i => i.Contacts)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == srnId);

            if (srn != null)
            {
                if (srn.Member != null)
                {
                    if (srn.Member.StakeholderManager != null)
                    {
                        var shm = srn.Member.StakeholderManager;
                        var placeholders = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("[SRNNumber]", srn.SRNNumber)
                        };

                        _emailService.SendEmail(shm.Email, shm.FirstName, "SRN Sale - File Submission Completion",
                            "SRNSaleRequestFileSubmissionCompletion.html", placeholders);
                    }

                    if (srn.Member.PrimaryBureau != null)
                    {
                        if (srn.Member.PrimaryBureau.Contacts.Count > 0)
                        {
                            var mainContactType = await _dbContext.ContactTypes
                                .AsNoTracking()
                                .FirstOrDefaultAsync(i => i.Name == "Main Contact Details");

                            var mainContact = srn.Member.PrimaryBureau
                                .Contacts
                                .FirstOrDefault(i => i.ContactTypeId == mainContactType.Id);

                            if (mainContact != null)
                            {
                                var bureau = mainContact;
                                var placeholders = new List<KeyValuePair<string, string>>
                                {
                                    new KeyValuePair<string, string>("[SRNNumber]", srn.SRNNumber)
                                };

                                _emailService.SendEmail(bureau.Email, bureau.FirstName,
                                    "SRN Sale - File Submission Completion",
                                    "SRNSaleRequestFileSubmissionCompletion.html", placeholders);
                            }
                        }
                    }

                    if (srn.Member.SecondaryBureau != null)
                    {
                        if (srn.Member.SecondaryBureau.Contacts.Count > 0)
                        {
                            var mainContactType = await _dbContext.ContactTypes
                                .AsNoTracking()
                                .FirstOrDefaultAsync(i => i.Name == "Main Contact Details");

                            var mainContact = srn.Member.SecondaryBureau
                                .Contacts
                                .FirstOrDefault(i => i.ContactTypeId == mainContactType.Id);

                            if (mainContact != null)
                            {
                                var bureau = mainContact;
                                var placeholders = new List<KeyValuePair<string, string>>
                                {
                                    new KeyValuePair<string, string>("[SRNNumber]", srn.SRNNumber)
                                };

                                _emailService.SendEmail(bureau.Email, bureau.FirstName,
                                    "SRN Sale - File Submission Completion",
                                    "SRNSaleRequestFileSubmissionCompletion.html", placeholders);
                            }
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to email Bureaus and Buyer SHM for SRN sale file submission completion. SRN Id " +
                          srnId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }

    public async Task NotifyBuyerMemberToStartNewSRNRegistrationProcess(int srnId, int buyerMemberId)
    {
        try
        {
            var srn = await _dbContext.SRNs
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == srnId);

            var buyerMember = await _dbContext.Members
                .AsNoTracking()
                .FirstOrDefaultAsync(x => x.Id == buyerMemberId);

            if (buyerMember != null)
            {
                var mainContact = await Helpers.Helpers.GetMemberMainContact(_dbContext, buyerMember.Id);

                if (mainContact != null)
                {
                    var buyer = mainContact;
                    var placeholders = new List<KeyValuePair<string, string>>
                    {
                        new KeyValuePair<string, string>("[SRNNumber]", (srn != null) ? srn.SRNNumber : "No SRN Number")
                    };

                    _emailService.SendEmail(buyer.Email, buyer.FirstName,
                        "SRN Sale - Request to Start New SRN Registration",
                        "SRNSaleRequestToStartNewSRNRegistrationProcessBuyerMember.html", placeholders);
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to email buyer member for SRN starting new SRN registration process. SRN Id " + srnId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }

    public async Task NotifyBuyerMemberToStartNewSRNRegistrationProcess_SplitOrMerge(int srnId, string requestType)
    {
        try
        {
            var srn = await _dbContext.SRNs
                .Include(i => i.Member)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == srnId);

            if (srn != null)
            {
                if (srn.Member != null)
                {
                    var mainContact = await Helpers.Helpers.GetMemberMainContact(_dbContext, srn.Member.Id);

                    if (mainContact != null)
                    {
                        var subject = "";
                        var template = "";
                        if (requestType == "merge")
                        {
                            subject = "SRN Merge - Request to Start New SRN Registration";
                            template = "SRNMergeRequestToStartNewSRNRegistrationProcess.html";
                        }

                        else if (requestType == "split")
                        {
                            subject = "SRN Split - Request to Start New SRN Registration";
                            template = "SRNSplitRequestToStartNewSRNRegistrationProcess.html";
                        }

                        var seller = mainContact;
                        var placeholders = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("[SRNNumber]", srn.SRNNumber)
                        };

                        _emailService.SendEmail(seller.Email, seller.FirstName, subject, template, placeholders);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to email buyer member for SRN starting new SRN registration process. SRN Id " + srnId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }

    public async Task NotifySellerAboutUnregisteredBuyer(int srnId)
    {
        try
        {
            var srn = await _dbContext.SRNs
                .Include(i => i.Member)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == srnId);

            if (srn != null)
            {
                if (srn.Member != null)
                {
                    var mainContact = await Helpers.Helpers.GetMemberMainContact(_dbContext, srn.Member.Id);

                    if (mainContact != null)
                    {
                        var seller = mainContact;
                        var placeholders = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("[SRNNumber]", srn.SRNNumber)
                        };

                        _emailService.SendEmail(seller.Email, seller.FirstName,
                            "SRN Sale - Buyer is NOT a SACRRA Member", "SRNSaleBuyerIsNotSACRRAMemberMember.html",
                            placeholders);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to email member about unregistered SRN buyer. SRN Id " + srnId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }

    public async Task NotifyMemberOfSRNSaleRequestCancellation(int srnId, string reviewComments)
    {
        try
        {
            var srn = await _dbContext.SRNs
                .Include(i => i.Member)
                .AsNoTracking()
                .FirstOrDefaultAsync(i => i.Id == srnId);

            if (srn != null)
            {
                if (srn.Member != null)
                {
                    var mainContact = await Helpers.Helpers.GetMemberMainContact(_dbContext, srn.Member.Id);

                    if (mainContact != null)
                    {
                        var seller = mainContact;
                        var message = srn.SRNNumber;
                        reviewComments = (!string.IsNullOrEmpty(reviewComments)) ? reviewComments : "No comment";

                        var placeHolders = new List<KeyValuePair<string, string>>
                        {
                            new KeyValuePair<string, string>("[CancellationReason]", reviewComments),
                            new KeyValuePair<string, string>("[SRNNumber]", srn.SRNNumber)
                        };

                        _emailService.SendEmail(seller.Email, seller.FirstName, "SRN Sale - Request Cancellation",
                            "SRNSaleRequestCancellationMember.html", placeHolders);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to email buyer member for SRN starting new SRN registration process. SRN Id " + srnId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }

    public async Task<List<VariableInstanceGetResource>> GetVariables(string processInstanceId)
    {
        try
        {
            using (var client = new HttpClient())
            {
                var uri = _configSettings.CamundaBaseAddress + "/variable-instance?processInstanceIdIn=" +
                          processInstanceId;
                var result = await client.GetAsync(uri);
                result.EnsureSuccessStatusCode();

                var resultString = await result.Content.ReadAsStringAsync();
                var variablesResourceList =
                    JsonConvert.DeserializeObject<List<VariableInstanceGetResource>>(resultString);

                return variablesResourceList;
            }
        }
        catch (Exception ex)
        {
            var message = "Unable to retrieve variables for process id " + processInstanceId;
            Sacrra.Membership.Business.Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }

    private async Task UpdateSRNHistory(string requestType, List<VariableInstanceGetResource> currentTaskVariables)
    {
        var changeType = "";
        var assigneeVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "stakeHolderManagerAssignee");
        var userId = 0;

        if (assigneeVariable != null)
            userId = Convert.ToInt32(assigneeVariable.Value);

        if (requestType == "sale")
        {
            var saleRequestVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "SRNSaleRequestId");
            var saleRequestId = 0;
            if (saleRequestVariable != null)
                saleRequestId = Convert.ToInt32(saleRequestVariable.Value);

            if (saleRequestId > 0)
            {
                var saleRequest = await _dbContext.SRNSaleRequests
                    .Include(i => i.BuyerSRN)
                    .AsNoTracking()
                    .FirstOrDefaultAsync(i => i.Id == saleRequestId);

                if (saleRequest.BuyerMemberId <= 0 && saleRequest.Type == SRNSaleType.Full)
                    throw new Exception("A full SRN sale has been requested but no buyer specified");

                if (saleRequest.BuyerMemberId > 0)
                {
                    var originalSRN = await _dbContext.SRNs
                        .AsNoTracking()
                        .FirstOrDefaultAsync(i => i.Id == saleRequest.SRNId);

                    if (saleRequest.Type == SRNSaleType.Full)
                    {
                        var compatibleSRNVariable =
                            currentTaskVariables.FirstOrDefault(i => i.Name == "CompatibleSRNExists");
                        var compatibleSRNExists = "";
                        if (compatibleSRNVariable != null)
                            compatibleSRNExists = compatibleSRNVariable.Value;

                        if (compatibleSRNExists == "yes")
                        {
                            var buyerSRNIdVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "BuyerSRNId");
                            var buyerSRNId = 0;
                            if (buyerSRNIdVariable != null)
                                buyerSRNId = (!string.IsNullOrEmpty(buyerSRNIdVariable.Value))
                                    ? Convert.ToInt32(buyerSRNIdVariable.Value)
                                    : 0;

                            /*
                             * If there's no buyer SRN Id specified (compatible SRN Id),
                             * then we tranfer the whole SRN to the buyer
                             */
                            if (buyerSRNId == 0)
                            {
                                if (originalSRN != null)
                                {
                                    originalSRN.MemberId = (int)saleRequest.BuyerMemberId;
                                    originalSRN.SPGroupId = saleRequest.SPGroupId;

                                    Helpers.Helpers.PrepareSRNForUpdate(_dbContext, originalSRN);

                                    _dbContext.Update(originalSRN);
                                    await _dbContext.SaveChangesAsync();
                                }
                            }
                        }
                    }

                    saleRequest.Status = SRNSaleStatus.Sold;

                    Helpers.Helpers.PrepareSRNSaleRequestForUpdate(_dbContext, saleRequest);
                    _dbContext.Update(saleRequest);
                    await _dbContext.SaveChangesAsync();

                    var stagingChangeLog = new MemberStagingChangeLogResource();
                    changeType = "SRN Sale";

                    if (originalSRN != null)
                    {
                        StagingChange stagingChange;

                        if (saleRequest.Type == SRNSaleType.Partial)
                        {
                            stagingChange = new StagingChange
                            {
                                Name = "SRN Sale",
                                OldValue = originalSRN.SRNNumber,
                                NewValue = saleRequest.BuyerSRN.SRNNumber
                            };
                        }
                        else
                        {
                            stagingChange = new StagingChange
                            {
                                Name = "SRN Sale",
                                OldValue = originalSRN.SRNNumber,
                                NewValue = originalSRN.SRNNumber
                            };
                        }

                        stagingChangeLog.Changes.Add(stagingChange);
                        var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);
                        await Helpers.Helpers.CreateEventLog(_dbContext, userId, changeType, originalSRN.TradingName,
                            "", changeBlob, originalSRN.Id, "SRN");
                    }
                }
            }
        }
        else if (requestType == "merge")
        {
            var mergeRequestVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "MergeToSRNId");
            var mergeToSRNId = 0;

            if (mergeRequestVariable != null)
                mergeToSRNId = Convert.ToInt32(mergeRequestVariable.Value);

            if (mergeToSRNId > 0)
            {
                var mergeListVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "SRNIdMergeList");

                if (mergeListVariable != null)
                {
                    var mergeList = mergeListVariable.Value.Split(',');
                    foreach (var srnId in mergeList)
                    {
                        var mergeRequest = await _dbContext.SRNMergeRequests
                            .Include(i => i.FromSRN)
                            .Include(i => i.ToSRN)
                            .FirstOrDefaultAsync(i => i.FromSRNId == Convert.ToInt32(srnId)
                                                      && i.ToSRNId == mergeToSRNId &&
                                                      i.Status == SRNMergeStatus.Requested);

                        if (mergeRequest != null)
                        {
                            mergeRequest.Status = SRNMergeStatus.Merged;
                            _dbContext.Update(mergeRequest);

                            var mergedSRN = await _dbContext.SRNs
                                .FirstOrDefaultAsync(i => i.Id == Convert.ToInt32(srnId));

                            var mergedStatus = await _dbContext.SRNStatuses
                                .FirstOrDefaultAsync(i => i.Name == "Deactivated - Merged");

                            mergedSRN.SRNStatusId = mergedStatus.Id;
                            mergedSRN.StatusLastUpdatedAt = DateTime.Now;
                            _dbContext.Update(mergedSRN);

                            var stagingChangeLog = new MemberStagingChangeLogResource();
                            changeType = "SRN Merge";
                            var stagingChange = new StagingChange
                            {
                                Name = "SRN Merge",
                                OldValue = mergeRequest.FromSRN.SRNNumber,
                                NewValue = mergeRequest.ToSRN.SRNNumber
                            };

                            stagingChangeLog.Changes.Add(stagingChange);
                            var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);
                            await Helpers.Helpers.CreateEventLog(_dbContext, userId, changeType,
                                mergeRequest.FromSRN.SRNNumber, "", changeBlob, mergeRequest.FromSRN.Id, "SRN");
                        }
                    }

                    await _dbContext.SaveChangesAsync();
                }
            }
        }

        else if (requestType == "split")
        {
            var splitRequestVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "SplitFromSRNId");
            var splitFromSRNId = 0;

            if (splitRequestVariable != null)
                splitFromSRNId = Convert.ToInt32(splitRequestVariable.Value);

            if (splitFromSRNId > 0)
            {
                var splitListVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "SRNIdSplitList");

                if (splitListVariable != null)
                {
                    var splitList = splitListVariable.Value.Split(',');
                    foreach (var srnId in splitList)
                    {
                        var splitRequest = await _dbContext.SRNSplitRequests
                            .Include(i => i.ToSRN)
                            .Include(i => i.FromSRN)
                            .FirstOrDefaultAsync(i => i.FromSRNId == splitFromSRNId
                                                      && i.ToSRNId == Convert.ToInt32(srnId) &&
                                                      i.Status == SRNSplitStatus.Requested);

                        if (splitRequest != null)
                        {
                            splitRequest.Status = SRNSplitStatus.Split;
                            _dbContext.Update(splitRequest);

                            var stagingChangeLog = new MemberStagingChangeLogResource();
                            changeType = "SRN Split";
                            var stagingChange = new StagingChange
                            {
                                Name = "SRN Split",
                                OldValue = splitRequest.FromSRN.SRNNumber,
                                NewValue = splitRequest.ToSRN.SRNNumber
                            };

                            stagingChangeLog.Changes.Add(stagingChange);
                            var changeBlob = JsonConvert.SerializeObject(stagingChangeLog);
                            await Helpers.Helpers.CreateEventLog(_dbContext, userId, changeType,
                                splitRequest.FromSRN.SRNNumber, "", changeBlob, splitRequest.FromSRN.Id, "SRN");
                        }
                    }

                    var actionTypeVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "SaleType");
                    var actionType = "";
                    if (actionTypeVariable != null)
                        actionType = actionTypeVariable.Value;

                    if (actionType == "full")
                    {
                        var splitSRN = await _dbContext.SRNs
                            .AsNoTracking()
                            .FirstOrDefaultAsync(i => i.Id == splitFromSRNId);

                        var splitStatus = await _dbContext.SRNStatuses
                            .FirstOrDefaultAsync(i => i.Name == "Deactivated - Split");

                        splitSRN.SRNStatusId = splitStatus.Id;
                        splitSRN.StatusLastUpdatedAt = DateTime.Now;

                        Helpers.Helpers.PrepareSRNSplitRequestForUpdate(_dbContext, splitSRN);
                        _dbContext.Update(splitSRN);
                    }

                    await _dbContext.SaveChangesAsync();
                }
            }
        }
    }

    public async Task ReAllocateSRNFromSellerToBuyer(string processIntanceId)
    {
        try
        {
            var currentTaskVariables = await GetVariables(processIntanceId);

            var requestTypeVariable = currentTaskVariables.FirstOrDefault(i => i.Name == "RequestType");
            var requestType = "";

            if (requestTypeVariable != null)
            {
                requestType = requestTypeVariable.Value;
            }

            if (!string.IsNullOrEmpty(requestType) && currentTaskVariables.Count > 0)
                await UpdateSRNHistory(requestType, currentTaskVariables);
        }
        catch (Exception ex)
        {
            var message = "Unable to re-allocate SRN from seller to buyer. Process Id " + processIntanceId;
            Helpers.Helpers.LogError(_dbContext, ex, message);
            throw new Exception(message);
        }
    }
}