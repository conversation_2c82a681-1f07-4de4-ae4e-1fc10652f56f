using AutoMapper;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Hosting.Internal;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Sacrra.Membership.Business.DTOs.AuthDTOs;
using Sacrra.Membership.Business.Services;
using Sacrra.Membership.Business.Services.LookupsService;
using Sacrra.Membership.Business.Services.MembersService;
using Sacrra.Membership.Business.Extensions;
using Sacrra.Membership.Business.Helpers;
using Sacrra.Membership.Business.MappingProfile;
using Sacrra.Membership.Business.Repositories;
using Sacrra.Membership.Business.Services.DataWarehouseService;
using Sacrra.Membership.Database;
using Sacrra.Membership.Notification.Helpers;
using Sacrra.Membership.Notification.Repositories;
using Serilog;
using System;
using System.Threading;
using Sacrra.Membership.Camunda.CamundaServices;
using Sacrra.Membership.Camunda.UserServices;
using Saccra.Membership.Business.Services.DynamicService;

namespace Sacrra.Membership.Camunda
{
    public class Program
    {
        static void Main(string[] args)
        {
            int maxRetryCounter = 0;
            string errorMessage = string.Empty;
            while (maxRetryCounter < 5)
            {
                try
                {
                    //Lets try to start camunda agent again
                    StartCamundaAgent();

                    //Reset the maxRetryCounter to zero if we manage to start camunda agent
                    maxRetryCounter = 0;
                }
                catch (Exception ex)
                {
                    errorMessage = $"{ ex.Message }. {ex.StackTrace}";

                    Log.Error(ex, $"An error occured while trying to run camunda agent. The app will be put on sleep for a couple of seconds before the next attempt to start camunda agent.");

                    //If an error occurs, lets put the app to sleep for X amount of minutes

                    switch (maxRetryCounter)
                    {
                        case 0:
                            Log.Information($"The app will attempt to start camunda agent in 120000 milliseconds.");
                            Thread.Sleep(120000);
                            break;
                        case 1:
                            Log.Information($"The app will attempt to start camunda agent in 240000 milliseconds.");
                            Thread.Sleep(240000);
                            break;
                        case 2:
                            Log.Information($"The app will attempt to start camunda agent in 360000 milliseconds.");
                            Thread.Sleep(360000);
                            break;
                        case 3:
                            Log.Information($"The app will attempt to start camunda agent in 480000 milliseconds.");
                            Thread.Sleep(480000);
                            break;
                        case 4:
                            Log.Information($"The app will attempt to start camunda agent in 600000 milliseconds.");
                            Thread.Sleep(600000);
                            break;
                    }
                    maxRetryCounter++;
                    Log.Information($"Starting camunda agent...retry count { maxRetryCounter }");
                }
            }

            var friendlyErrorMessage = $"An error occured while trying to run camunda agent. Max retries reached { maxRetryCounter }. The app is shutting down.";
            Log.Error(friendlyErrorMessage);

            //Send email about camunda agent failure
            SendEmail($"{friendlyErrorMessage} <br /> { errorMessage }");
        }

        private static void ConfigureServices(ServiceCollection services)
        {
            var connectionString = Helpers.Configuration.GetSection("ConnectionString").Value;
            services.AddDbContext<AppDbContext>(x => x.UseSqlServer(connectionString,
            b => b.MigrationsAssembly("EventManager.Database")));

            var mappingConfig = new MapperConfiguration(mc =>
            {
                mc.AddProfile(new MappingProfile());
            });

            var mapper = mappingConfig.CreateMapper();
            services.AddSingleton(mapper);

            services.AddHttpContextAccessor();

            services.AddSingleton(Helpers.Configuration);
            services.AddScoped<LookupsRepository>();
            services.AddScoped<AuthRepository>();
            services.AddScoped<UserRepository>();
            services.AddScoped<BureauRepository>();
            services.AddScoped<TradingNameRepository>();
            services.AddScoped<MemberRepository>();
            services.AddScoped<CamundaRepository>();
            services.AddScoped<IHostingEnvironment, HostingEnvironment>();
            services.AddScoped<EmailService>();
            services.AddScoped<ContactTypeRepository>();
            services.AddScoped<SRNRepository>();
            services.AddScoped<SRNStatusRepository>();
            services.AddScoped<BranchLocationRepository>();
            services.AddScoped<AccountTypeRepository>();
            services.AddScoped<SoftwareVendorRepository>();
            services.AddScoped<NCRReportingAccountTypeClassificationRepository>();
            services.AddScoped<LoanManagementSystemVendorRepository>();
            services.AddScoped<ALGRepository>();
            services.AddScoped<MemberExtensions>();
            services.AddScoped<SRNExtensions>();
            services.AddScoped<SRNStatusReasonRepository>();
            services.AddScoped<DWExceptionRepository>();
            services.AddScoped<AdHocFileSubmissionsCamundaService>();
            services.AddScoped<MemberAutoCloseOnSRNClosureCamundaService>();
            services.AddScoped<MemberStatusUpdateCamundaService>();
            services.AddScoped<MemberUpdateDetailsCamundaService>();
            services.AddScoped<NewMemberTakeonCamundaService>();
            services.AddScoped<ReplacementFileSubmissionCamundaService>();
            services.AddScoped<SrnSellingCamundaService>();
            services.AddScoped<SrnStatusUpdateNonCancellationsCamundaService>();
            services.AddScoped<SrnStatusUpdateToTestCamundaService>();
            services.AddScoped<SrnUpdateDetailsCamundaService>();
            services.AddScoped<NewSrnApplicationCamundaService>();
            services.AddScoped<SrnStatusUpdateCamundaService>();
            
            services.AddOptions();

            //Version 1 configs
            services.Configure<ConfigSettings>(Helpers.Configuration.GetSection("ConfigSettings"));
            services.Configure<EmailSettings>(Helpers.Configuration.GetSection("EmailSettings"));
            services.Configure<Auth0>(Helpers.Configuration.GetSection("Auth0"));
            services.Configure<Auth0APIManagement>(Helpers.Configuration.GetSection("Auth0APIManagement"));

            //API version 2 services
            services.AddScoped<AuthenticationService>();
            services.AddScoped<MembersService>();
            services.AddScoped<CamundaService>();
            services.AddScoped<SRNService>();
            services.AddScoped<MemberServiceHelper>();
            services.AddScoped<SRNServiceHelper>();
            services.Configure<Auth0InputDTO>(Helpers.Configuration.GetSection("Auth0"));
            services.Configure<Auth0SettingsDTO>(Helpers.Configuration.GetSection("Auth0APIManagement"));
            services.AddScoped<CamundaServiceHelper>();
            services.AddScoped<DWExceptionService>();
            services.AddScoped<LookupsService>();
            services.AddScoped<LookupsServiceHelper>();
            services.AddScoped<MailgunService>();
            services.AddScoped<DataWarehouseService>();
            //services.AddScoped<CompanyService>();
            services.AddScoped<NCRReportingAccountTypeClassificationsService>();
            services.AddScoped<NewSrnApplicationUserService>();

            //API version 2 helpers
            services.AddScoped<GlobalHelper>();
            services.AddScoped<AuthenticationServiceHelper>();
            services.AddSingleton<CamundaAgent>();

            //Dynamic Api
            services.AddScoped<DynamicService>();

            services.AddScoped<HangfireService>();

            Log.Logger = new LoggerConfiguration()
                .ReadFrom.Configuration(Helpers.Configuration)
                .CreateLogger();
        }
        private static void StartCamundaAgent()
        {
            var services = new ServiceCollection();

            ConfigureServices(services);

            using var serviceProvider = services.BuildServiceProvider();
            var camundaAgent = serviceProvider.GetRequiredService<CamundaAgent>();

            camundaAgent.Run().Wait();
        }

        private static void SendEmail(string errorMessage)
        {
            var services = new ServiceCollection();

            ConfigureServices(services);

            using (ServiceProvider serviceProvider = services.BuildServiceProvider())
            {
                var emailService = serviceProvider.GetRequiredService<EmailService>();
                var camundaRepository = serviceProvider.GetRequiredService<CamundaRepository>();

                var recipients = camundaRepository.GetCamundaErrorRecipients();

                foreach(var recipient in recipients)
                {
                    string subject = "Camunda Service Error - App Stopped Running";
                    string messageBody = $"Hi { recipient.FirstName },<br /><br />";
                    messageBody += $"The camunda service app has stopped. See error message below<br /><br />";

                    messageBody += $"{ errorMessage }";

                    emailService.SendEmail(recipient.Email, recipient.FirstName, subject, messageBody);
                }
            }
        }
    }
}
