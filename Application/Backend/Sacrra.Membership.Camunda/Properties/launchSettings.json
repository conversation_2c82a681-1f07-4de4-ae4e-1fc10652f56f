{"$schema": "http://json.schemastore.org/launchsettings.json", "profiles": {"LocalDev Sacrra Camunda Service": {"commandName": "Project", "launchBrowser": false, "launchUrl": "swagger", "applicationUrl": "http://0.0.0.0:5081", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "LocalDevelopment"}}, "Dev Sacrra Camunda Service": {"commandName": "Project", "launchBrowser": false, "launchUrl": "swagger", "applicationUrl": "http://0.0.0.0:5081", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "UAT Sacrra Camunda Service": {"commandName": "Project", "launchBrowser": false, "launchUrl": "swagger", "applicationUrl": "http://0.0.0.0:5081", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "UAT"}}, "PROD Sacrra Camunda Service": {"commandName": "Project", "launchBrowser": false, "launchUrl": "swagger", "applicationUrl": "http://0.0.0.0:5081", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Production"}}}}