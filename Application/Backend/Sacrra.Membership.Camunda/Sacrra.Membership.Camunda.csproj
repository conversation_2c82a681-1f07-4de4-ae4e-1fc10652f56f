<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>Exe</OutputType>
		<TargetFramework>net6.0</TargetFramework>
	</PropertyGroup>

	<ItemGroup>
		<Content Include="..\Sacrra.Membership.Api\AppSettings\appsettings.Development.json" Link="appsettings.Development.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\AppSettings\appsettings.DockerLocal.json" Link="appsettings.DockerLocal.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\AppSettings\appsettings.json" Link="appsettings.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\AppSettings\appsettings.LocalDevelopment.json" Link="appsettings.LocalDevelopment.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\AppSettings\appsettings.Production.json" Link="appsettings.Production.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\AppSettings\appsettings.UAT.json" Link="appsettings.UAT.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\ALGClientMemberApplicationRejectedApplicantLeader.html" Link="mailtemplates\ALGClientMemberApplicationRejectedApplicantLeader.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\EmailBureausAboutSRNPendingClosure.html" Link="mailtemplates\EmailBureausAboutSRNPendingClosure.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\EmailBureausAboutSRNStatusUpdateForNonCancellations.html" Link="mailtemplates\EmailBureausAboutSRNStatusUpdateForNonCancellations.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\EmailBureausAfterSRNTesting.html" Link="mailtemplates\EmailBureausAfterSRNTesting.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\EmailBureausSRNIsLive.html" Link="mailtemplates\EmailBureausSRNIsLive.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\EmailBureausToClosePaymentProfile.html" Link="mailtemplates\EmailBureausToClosePaymentProfile.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\EmailConfirmation.html" Link="mailtemplates\EmailConfirmation.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\ReplacementFileSubmissionDeclined.html" Link="mailtemplates\ReplacementFileSubmissionDeclined.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\ReplacementFileSubmissionApproved.html" Link="mailtemplates\ReplacementFileSubmissionApproved.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\ReplacementFileSubmissionCancelled.html" Link="mailtemplates\ReplacementFileSubmissionCancelled.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\EmailMemberAboutSRNPendingClosure.html" Link="mailtemplates\EmailMemberAboutSRNPendingClosure.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\EmailMemberAboutSRNStatusUpdateForNonCancellations.html" Link="mailtemplates\EmailMemberAboutSRNStatusUpdateForNonCancellations.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\EmailMemberToConfirmClosure.html" Link="mailtemplates\EmailMemberToConfirmClosure.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\EmailMemberToStateReactivation.html" Link="mailtemplates\EmailMemberToStateReactivation.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\EmailMemberToStateRemoval.html" Link="mailtemplates\EmailMemberToStateRemoval.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\MailgunFailedEvents.html" Link="mailtemplates\MailgunFailedEvents.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\MemberApplicationCancellationApplicant.html" Link="mailtemplates\MemberApplicationCancellationApplicant.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\MemberApplicationRejectedApplicant.html" Link="mailtemplates\MemberApplicationRejectedApplicant.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\MemberAssessmentPaymentReminderApplicant.html" Link="mailtemplates\MemberAssessmentPaymentReminderApplicant.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\MemberNewALGClientRegistrationALGLeader.html" Link="mailtemplates\MemberNewALGClientRegistrationALGLeader.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\MemberOnboardingPaymentReminderApplicant.html" Link="mailtemplates\MemberOnboardingPaymentReminderApplicant.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\MembershipApplicationPaymentAndConstitutionReminderApplicant.html" Link="mailtemplates\MembershipApplicationPaymentAndConstitutionReminderApplicant.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\MembershipApplicationSuccessfulApplicant.html" Link="mailtemplates\MembershipApplicationSuccessfulApplicant.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\MemberUpdateAcceptedApplicant.html" Link="mailtemplates\MemberUpdateAcceptedApplicant.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\MemberUpdateDeclinedApplicant.html" Link="mailtemplates\MemberUpdateDeclinedApplicant.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\MemberUpdateNoApprovalSHM.html" Link="mailtemplates\MemberUpdateNoApprovalSHM.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\SRNApplicationBureaus.html" Link="mailtemplates\SRNApplicationBureaus.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\SRNApplicationRejectionApplicant.html" Link="mailtemplates\SRNApplicationRejectionApplicant.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\SRNApplicationSuccessfulApplicant.html" Link="mailtemplates\SRNApplicationSuccessfulApplicant.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\SRNMergeRequestRejectedApplicant.html" Link="mailtemplates\SRNMergeRequestRejectedApplicant.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\SRNMergeRequestToStartNewSRNRegistrationProcess.html" Link="mailtemplates\SRNMergeRequestToStartNewSRNRegistrationProcess.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\SRNReviewFailureSHM.html" Link="mailtemplates\SRNReviewFailureSHM.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\BureauUnsuccessfulLoad.html" Link="mailtemplates\BureauUnsuccessfulLoad.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\SRNSaleBuyerIsNotSACRRAMemberMember.html" Link="mailtemplates\SRNSaleBuyerIsNotSACRRAMemberMember.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\SRNSaleRequestCancellationMember.html" Link="mailtemplates\SRNSaleRequestCancellationMember.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\SRNSaleRequestCommencementSellerMember.html" Link="mailtemplates\SRNSaleRequestCommencementSellerMember.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\SRNSaleRequestCommencementSellerSHM.html" Link="mailtemplates\SRNSaleRequestCommencementSellerSHM.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\SRNSaleRequestFileSubmissionCompletion.html" Link="mailtemplates\SRNSaleRequestFileSubmissionCompletion.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\SRNSaleRequestToStartNewSRNRegistrationProcessBuyerMember.html" Link="mailtemplates\SRNSaleRequestToStartNewSRNRegistrationProcessBuyerMember.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\SRNSaleToUnconfirmedBuyerAcceptedSellerMember.html" Link="mailtemplates\SRNSaleToUnconfirmedBuyerAcceptedSellerMember.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\SRNSplitRequestRejectedApplicant.html" Link="mailtemplates\SRNSplitRequestRejectedApplicant.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\SRNSplitRequestToStartNewSRNRegistrationProcess.html" Link="mailtemplates\SRNSplitRequestToStartNewSRNRegistrationProcess.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\SRNUpdateAcceptedApplicant.html" Link="mailtemplates\SRNUpdateAcceptedApplicant.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\SRNUpdateDeclinedApplicant.html" Link="mailtemplates\SRNUpdateDeclinedApplicant.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\SRNUpdateNoApprovalSHM.html" Link="mailtemplates\SRNUpdateNoApprovalSHM.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\UserAccountCreated.html" Link="mailtemplates\UserAccountCreated.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\AdHocFileSubmissionApproved.html" Link="mailtemplates\AdHocFileSubmissionApproved.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\AdHocFileSubmissionCancelled.html" Link="mailtemplates\AdHocFileSubmissionCancelled.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Sacrra.Membership.Api\wwwroot\mailtemplates\AdHocFileSubmissionDeclined.html" Link="mailtemplates\AdHocFileSubmissionDeclined.html">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.2.2" />
		<PackageReference Include="Microsoft.AspNetCore.Hosting" Version="2.2.7" />
		<PackageReference Include="Microsoft.Extensions.Configuration" Version="6.0.1" />
		<PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions" Version="6.0.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="6.0.0" />
		<PackageReference Include="Serilog" Version="2.11.0" />
		<PackageReference Include="Serilog.Settings.Configuration" Version="3.3.0" />
		<PackageReference Include="Serilog.Sinks.AzureBlobStorage" Version="3.1.3" />
		<PackageReference Include="Serilog.Sinks.Console" Version="4.0.1" />
		<PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
		<PackageReference Include="SSH.NET" Version="2020.0.2" />
		<PackageReference Include="System.Configuration.ConfigurationManager" Version="6.0.0" />
		<PackageReference Include="Serilog.Sinks.SyslogServer" Version="1.1.1" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Saccra.Membership.Business\Saccra.Membership.Business.csproj" />
		<ProjectReference Include="..\Sacrra.Membership.Reporting\Sacrra.Membership.Reporting.csproj" />
	</ItemGroup>

	<ItemGroup>
		<None Update="appsettings.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="appsettings.Development.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</None>
		<None Update="bpmn\member-auto-close-on-srn-closure.bpmn">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Remove="bpmn\srn-status-update-test-V2.bpmn" />
		<Content Include="bpmn\srn-status-update-test-V2.bpmn">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<None Remove="bpmn\srn-update-details-V2.bpmn" />
		<Content Include="bpmn\srn-update-details-V2.bpmn">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<None Remove="bpmn\srn-status-update-V2.bpmn" />
		<Content Include="bpmn\srn-status-update-V2.bpmn">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
		<None Update="bpmn\member-status-update-V1.bpmn">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="bpmn\member-update-details-V1.bpmn">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="bpmn\New-DW-Exception-V1.bpmn">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="bpmn\new-member-takeon-V2.bpmn">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="bpmn\new-member-takeon-V3.bpmn">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="bpmn\new-member-takeon-V5.bpmn">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="bpmn\new-member-takeon.bpmn">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="bpmn\new-srn-old-to-be-removed.bpmn">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="bpmn\SACRRA_Non_Member_Workflow.bpmn">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="bpmn\SRN-Selling-V3.bpmn">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="bpmn\srn-status-update-non-cancellations-V1.bpmn">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="bpmn\srn-status-update-test-V1.bpmn">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="bpmn\srn-status-update-V1.bpmn">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="bpmn\srn-update-details-V1.bpmn">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="bpmn\SRNApplicationV3.bpmn">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="bpmn\Replacement-File-Submissions-v4.bpmn">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="bpmn\Ad-Hoc-File-Submissions-V2.bpmn">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="Properties\launchSettings.json">
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>Never</CopyToPublishDirectory>
		</None>
	</ItemGroup>

	<ItemGroup>
		<Folder Include="logs\" />
		<Folder Include="mailtemplates\" />
	</ItemGroup>

	<ItemGroup>
		<None Remove="bpmn\SRNApplicationV3.bpmn" />
		<Content Include="bpmn\SRNApplicationV3.bpmn">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

</Project>
