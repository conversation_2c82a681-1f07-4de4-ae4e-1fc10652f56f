<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bioc="http://bpmn.io/schema/bpmn/biocolor/1.0" id="Definitions_053lmvj" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.33.1">
  <bpmn:collaboration id="Collaboration_008vpn1">
    <bpmn:participant id="Participant_1kh7vyo" name="SRN Status Update to Test" processRef="SRN-Status-Update-To-Test" />
  </bpmn:collaboration>
  <bpmn:process id="SRN-Status-Update-To-Test" isExecutable="true">
    <bpmn:laneSet id="LaneSet_1bge26e">
      <bpmn:lane id="Lane_035400c" name="SACRRA Portal">
        <bpmn:flowNodeRef>Task_1yp2wlm</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_EmailBureausAfterTesting</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_UpdateSRNStatus</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_0587rdi" name="SHM">
        <bpmn:flowNodeRef>ExclusiveGateway_1rfunjz</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>Task_IsSRNLive</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>ExclusiveGateway_0e1bn4z</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>IntermediateThrowEvent_1uwvrpk</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>IntermediateThrowEvent_1f16z8z</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>StartEvent_023p81v</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>UserTask_ConfirmMigrationTesting</bpmn:flowNodeRef>
      </bpmn:lane>
      <bpmn:lane id="Lane_0jk0yud" name="SACRRA Admin">
        <bpmn:flowNodeRef>Task_CompleteAndUpdateDTH</bpmn:flowNodeRef>
        <bpmn:flowNodeRef>EndEvent_07e2l45</bpmn:flowNodeRef>
      </bpmn:lane>
    </bpmn:laneSet>
    <bpmn:serviceTask id="Task_1yp2wlm" name="Email bureaus on Go Live" camunda:type="external" camunda:topic="email-bureaus-on-srn-go-live">
      <bpmn:incoming>SequenceFlow_0cjhodd</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0xw4ch6</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:exclusiveGateway id="ExclusiveGateway_1rfunjz" name="Is confirmed?">
      <bpmn:incoming>SequenceFlow_0b9pc4r</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0decq40</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_1wlnm0p</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_05q07sc</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:userTask id="Task_IsSRNLive" name="Is SRN Live?" camunda:assignee="${stakeHolderManagerAssignee}">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="IsSRNLive" type="enum">
            <camunda:value id="yes" name="Yes" />
            <camunda:value id="no" name="No" />
            <camunda:value id="cancel" name="Cancel" />
            <camunda:value />
          </camunda:formField>
          <camunda:formField id="goLiveDate" type="string" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_154ifyz</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_17c8lit</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:exclusiveGateway id="ExclusiveGateway_0e1bn4z" name="Is live?">
      <bpmn:incoming>SequenceFlow_17c8lit</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_17u1fi4</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_0cjhodd</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_1sl2m65</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:serviceTask id="Task_EmailBureausAfterTesting" name="Email bureaus after testing" camunda:type="external" camunda:topic="email-bureaus-after-srn-testing-srn-status-update">
      <bpmn:incoming>SequenceFlow_1wlnm0p</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_141cd5l</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:userTask id="Task_CompleteAndUpdateDTH" name="Make it live on DTH" camunda:assignee="${SACRRAAdminAssignee}" camunda:candidateGroups="SACRRAAdministrator">
      <bpmn:extensionElements />
      <bpmn:incoming>SequenceFlow_0xw4ch6</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0ms3enw</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:intermediateCatchEvent id="IntermediateThrowEvent_1uwvrpk">
      <bpmn:incoming>SequenceFlow_141cd5l</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_17u1fi4</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_154ifyz</bpmn:outgoing>
      <bpmn:timerEventDefinition>
        <bpmn:timeDate xsi:type="bpmn:tFormalExpression">${goLiveDate}</bpmn:timeDate>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:endEvent id="EndEvent_07e2l45">
      <bpmn:incoming>SequenceFlow_0ms3enw</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_1sl2m65</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_05q07sc</bpmn:incoming>
      <bpmn:terminateEventDefinition />
    </bpmn:endEvent>
    <bpmn:intermediateCatchEvent id="IntermediateThrowEvent_1f16z8z">
      <bpmn:incoming>Flow_1dtabsh</bpmn:incoming>
      <bpmn:incoming>SequenceFlow_0decq40</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_01h21a6</bpmn:outgoing>
      <bpmn:timerEventDefinition>
        <bpmn:timeDate xsi:type="bpmn:tFormalExpression">${testEndDate}</bpmn:timeDate>
      </bpmn:timerEventDefinition>
    </bpmn:intermediateCatchEvent>
    <bpmn:sequenceFlow id="SequenceFlow_0cjhodd" name="Yes" sourceRef="ExclusiveGateway_0e1bn4z" targetRef="Task_1yp2wlm">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${IsSRNLive=="yes"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0xw4ch6" sourceRef="Task_1yp2wlm" targetRef="Task_CompleteAndUpdateDTH" />
    <bpmn:sequenceFlow id="SequenceFlow_01h21a6" sourceRef="IntermediateThrowEvent_1f16z8z" targetRef="UserTask_ConfirmMigrationTesting" />
    <bpmn:sequenceFlow id="SequenceFlow_0b9pc4r" sourceRef="UserTask_ConfirmMigrationTesting" targetRef="ExclusiveGateway_1rfunjz" />
    <bpmn:sequenceFlow id="SequenceFlow_1wlnm0p" name="Yes" sourceRef="ExclusiveGateway_1rfunjz" targetRef="Task_EmailBureausAfterTesting">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${IsTestingMigrationConfirmed=="yes"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_0decq40" name="No" sourceRef="ExclusiveGateway_1rfunjz" targetRef="IntermediateThrowEvent_1f16z8z">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${IsTestingMigrationConfirmed=="no"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_05q07sc" name="Cancel" sourceRef="ExclusiveGateway_1rfunjz" targetRef="EndEvent_07e2l45">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${IsTestingMigrationConfirmed=="cancel"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_154ifyz" sourceRef="IntermediateThrowEvent_1uwvrpk" targetRef="Task_IsSRNLive" />
    <bpmn:sequenceFlow id="SequenceFlow_17c8lit" sourceRef="Task_IsSRNLive" targetRef="ExclusiveGateway_0e1bn4z" />
    <bpmn:sequenceFlow id="SequenceFlow_17u1fi4" name="No" sourceRef="ExclusiveGateway_0e1bn4z" targetRef="IntermediateThrowEvent_1uwvrpk">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${IsSRNLive=="no"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_1sl2m65" name="Cancel" sourceRef="ExclusiveGateway_0e1bn4z" targetRef="EndEvent_07e2l45">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${IsSRNLive=="cancel"}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_141cd5l" sourceRef="Task_EmailBureausAfterTesting" targetRef="IntermediateThrowEvent_1uwvrpk" />
    <bpmn:sequenceFlow id="SequenceFlow_0ms3enw" sourceRef="Task_CompleteAndUpdateDTH" targetRef="EndEvent_07e2l45" />
    <bpmn:sequenceFlow id="Flow_11lq3am" sourceRef="StartEvent_023p81v" targetRef="Task_UpdateSRNStatus" />
    <bpmn:sequenceFlow id="Flow_1dtabsh" sourceRef="Task_UpdateSRNStatus" targetRef="IntermediateThrowEvent_1f16z8z" />
    <bpmn:startEvent id="StartEvent_023p81v">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="testEndDate" label="Test End Date" type="date" />
          <camunda:formField id="SRNId" label="SRN Id" type="long" />
          <camunda:formField id="SRNUpdateType" label="SRN Update Type" type="long" />
          <camunda:formField id="newSrn" type="boolean" defaultValue="true" />
          <camunda:formField id="srnNumber" type="string" />
          <camunda:formField id="stakeHolderManagerAssignee" label="StakeHolderManagerAssignee" type="string" />
          <camunda:formField id="processInstanceId" label="processInstanceId" type="string" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:outgoing>Flow_11lq3am</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="UserTask_ConfirmMigrationTesting" name="Is testing confirmed?" camunda:assignee="${stakeHolderManagerAssignee}" camunda:candidateGroups="StakeHolderManager">
      <bpmn:extensionElements>
        <camunda:formData>
          <camunda:formField id="IsTestingMigrationConfirmed" type="enum">
            <camunda:value id="yes" name="Yes" />
            <camunda:value id="no" name="No" />
            <camunda:value id="cancel" name="Cancel" />
            <camunda:value />
          </camunda:formField>
          <camunda:formField id="goLiveDate" type="string" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>SequenceFlow_01h21a6</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_0b9pc4r</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:serviceTask id="Task_UpdateSRNStatus" name="Update SRN &#38; SRN File Statuses" camunda:type="external" camunda:topic="update-srn-and-file-statuses">
      <bpmn:incoming>Flow_11lq3am</bpmn:incoming>
      <bpmn:outgoing>Flow_1dtabsh</bpmn:outgoing>
    </bpmn:serviceTask>
  </bpmn:process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Collaboration_008vpn1">
      <bpmndi:BPMNShape id="Participant_1kh7vyo_di" bpmnElement="Participant_1kh7vyo" isHorizontal="true">
        <dc:Bounds x="152" y="82" width="1498" height="1133" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_0jk0yud_di" bpmnElement="Lane_0jk0yud" isHorizontal="true">
        <dc:Bounds x="182" y="1025" width="1468" height="190" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_0587rdi_di" bpmnElement="Lane_0587rdi" isHorizontal="true">
        <dc:Bounds x="182" y="82" width="1468" height="722" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Lane_035400c_di" bpmnElement="Lane_035400c" isHorizontal="true">
        <dc:Bounds x="182" y="804" width="1468" height="222" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1nam24e_di" bpmnElement="Task_1yp2wlm">
        <dc:Bounds x="1190" y="905" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1rfunjz_di" bpmnElement="ExclusiveGateway_1rfunjz" isMarkerVisible="true" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="975" y="559" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1034.5" y="577" width="67" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_0iycr5t_di" bpmnElement="Task_IsSRNLive">
        <dc:Bounds x="1180" y="565" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_0e1bn4z_di" bpmnElement="ExclusiveGateway_0e1bn4z" isMarkerVisible="true" bioc:stroke="#000" bioc:fill="#fff">
        <dc:Bounds x="1325" y="580" width="50" height="50" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1382" y="608" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1mbfwun_di" bpmnElement="Task_EmailBureausAfterTesting">
        <dc:Bounds x="950" y="905" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_09e25ax_di" bpmnElement="Task_CompleteAndUpdateDTH">
        <dc:Bounds x="1190" y="1105" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="IntermediateCatchEvent_0nuvdve_di" bpmnElement="IntermediateThrowEvent_1uwvrpk">
        <dc:Bounds x="1132" y="727" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1hi5ye2_di" bpmnElement="EndEvent_07e2l45">
        <dc:Bounds x="1382" y="1127" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="IntermediateCatchEvent_1g9qriu_di" bpmnElement="IntermediateThrowEvent_1f16z8z">
        <dc:Bounds x="562" y="566" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="883" y="540" width="50" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="StartEvent_023p81v_di" bpmnElement="StartEvent_023p81v">
        <dc:Bounds x="426" y="566" width="36" height="36" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="UserTask_09nap4b_di" bpmnElement="UserTask_ConfirmMigrationTesting">
        <dc:Bounds x="700" y="544" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_0yorbl1_di" bpmnElement="Task_UpdateSRNStatus">
        <dc:Bounds x="460" y="860" width="100" height="80" />
        <bpmndi:BPMNLabel />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_0cjhodd_di" bpmnElement="SequenceFlow_0cjhodd">
        <di:waypoint x="1350" y="630" />
        <di:waypoint x="1350" y="945" />
        <di:waypoint x="1290" y="945" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1321" y="747" width="19" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0xw4ch6_di" bpmnElement="SequenceFlow_0xw4ch6">
        <di:waypoint x="1240" y="985" />
        <di:waypoint x="1240" y="1105" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_01h21a6_di" bpmnElement="SequenceFlow_01h21a6">
        <di:waypoint x="598" y="584" />
        <di:waypoint x="700" y="584" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0b9pc4r_di" bpmnElement="SequenceFlow_0b9pc4r">
        <di:waypoint x="800" y="584" />
        <di:waypoint x="975" y="584" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1wlnm0p_di" bpmnElement="SequenceFlow_1wlnm0p">
        <di:waypoint x="1000" y="609" />
        <di:waypoint x="1000" y="905" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="870" y="593" width="19" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0decq40_di" bpmnElement="SequenceFlow_0decq40" bioc:stroke="#000" bioc:fill="#fff">
        <di:waypoint x="994" y="565" />
        <di:waypoint x="975" y="510" />
        <di:waypoint x="590" y="510" />
        <di:waypoint x="590" y="569" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="978" y="553" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_05q07sc_di" bpmnElement="SequenceFlow_05q07sc">
        <di:waypoint x="1004" y="563" />
        <di:waypoint x="1030" y="440" />
        <di:waypoint x="1530" y="440" />
        <di:waypoint x="1530" y="1145" />
        <di:waypoint x="1420" y="1145" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1038" y="461" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_154ifyz_di" bpmnElement="SequenceFlow_154ifyz">
        <di:waypoint x="1150" y="727" />
        <di:waypoint x="1150" y="605" />
        <di:waypoint x="1180" y="605" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_17c8lit_di" bpmnElement="SequenceFlow_17c8lit">
        <di:waypoint x="1280" y="605" />
        <di:waypoint x="1325" y="605" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_17u1fi4_di" bpmnElement="SequenceFlow_17u1fi4">
        <di:waypoint x="1350" y="580" />
        <di:waypoint x="1350" y="494" />
        <di:waypoint x="1120" y="494" />
        <di:waypoint x="1120" y="730" />
        <di:waypoint x="1141" y="730" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1332" y="528" width="15" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_1sl2m65_di" bpmnElement="SequenceFlow_1sl2m65">
        <di:waypoint x="1375" y="605" />
        <di:waypoint x="1400" y="605" />
        <di:waypoint x="1400" y="1127" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1402" y="768" width="35" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_141cd5l_di" bpmnElement="SequenceFlow_141cd5l">
        <di:waypoint x="1050" y="945" />
        <di:waypoint x="1150" y="945" />
        <di:waypoint x="1150" y="763" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_0ms3enw_di" bpmnElement="SequenceFlow_0ms3enw">
        <di:waypoint x="1290" y="1145" />
        <di:waypoint x="1382" y="1145" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_11lq3am_di" bpmnElement="Flow_11lq3am">
        <di:waypoint x="444" y="602" />
        <di:waypoint x="444" y="900" />
        <di:waypoint x="460" y="900" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="Flow_1dtabsh_di" bpmnElement="Flow_1dtabsh">
        <di:waypoint x="560" y="900" />
        <di:waypoint x="580" y="900" />
        <di:waypoint x="580" y="602" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
