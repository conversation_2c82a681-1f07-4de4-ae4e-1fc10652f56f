using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace Sacrra.Membership.Database.Enums
{
    public enum AdhocFileBureauStatuses
    {
        [Display(Name = "Bureau Load Pending")]
        BureauLoadPending = 1,

        [Display(Name = "Bureau Load Successful")]
        BureauLoadSuccessful = 2,

        [Display(Name = "Bureau Load Unsuccessful")]
        BureauLoadUnsuccessful = 3,

        [Display(Name = "Bureau Feedback Stats Pending")]
        BureauFeedbackStatsPending = 4,

        [Display(Name = "Bureau Feedback Stats Available")]
        BureauFeedbackStatsAvailable = 5
    }
}
