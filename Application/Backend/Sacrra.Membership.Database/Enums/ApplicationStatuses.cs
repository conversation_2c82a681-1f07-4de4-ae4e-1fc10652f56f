using System.ComponentModel.DataAnnotations;

namespace Sacrra.Membership.Database.Enums
{
    public enum ApplicationStatuses
    {
        [Display(Name = "Member Registration: Submitted")]
        MemberRegistrationSubmitted = 1,

        [Display(Name = "Member Registration: Review")]
        MemberRegistrationReview = 2,

        [Display(Name = "Member Registration: Disqualified")]
        MemberRegistrationDisqualified = 3,

        [Display(Name = "Member Registration: Awaiting Payment")]
        MemberRegistrationAwaitingPayment = 4,

        [Display(Name = "Member Registration: Payment Received")]
        MemberRegistrationPaymentReceived = 5,

        [Display(Name = "Member Registration: Approved")]
        MemberRegistrationApproved = 6,

        [Display(Name = "Member Registration: Completed")]
        MemberRegistrationCompleted = 7,

        [Display(Name = "Member Registration: Awaiting Generation of Initial Assessment Invoice")]
        MemberRegistrationAwaitingInitialAssessmentInvoiceGeneration = 8,

        [Display(Name = "Member Registration: Awaiting Initial Assessment Invoice Payment")]
        MemberRegistrationAwaitingInitialAssessmentInvoicePayment = 9,

        [Display(Name = "Member Registration: SHM Final Review")]
        MemberRegistrationSHMFinalReview = 10,

        [Display(Name = "Member Registration: Cancelled. Initial Assessment Invoice not Paid")]
        MemberRegistrationCancelled_InitialAssessmentInvoiceNotPaid = 11,

        [Display(Name = "Member Registration: Awaiting Onboarding Invoice Payment")]
        MemberRegistrationAwaitingOnboardingInvoicePayment = 12,

        [Display(Name = "Member Registration: Waiting for Invoice to be Generated")]
        MemberRegistrationWaitingForInvoiceToBeGenerated = 13,

        [Display(Name = "Cancelled")]
        MemberRegistrationCancelled = 14,

        [Display(Name = "Active")]
        MemberRegistrationActive = 15,
    }
}
