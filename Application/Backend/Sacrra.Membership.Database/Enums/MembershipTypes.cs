using System.ComponentModel.DataAnnotations;

namespace Sacrra.Membership.Database.Enums
{
    public enum MembershipTypes
    {
        [Display(Name = "Full Member")]
        FullMember = 1,
        [Display(Name = "Non Member")]
        NonMember = 2,
        [Display(Name = "Affiliate")]
        Affiliate = 3,
        [Display(Name = "ALG Client")]
        ALGClient = 4,
        [Display(Name = "ALG Leader")]
        ALGLeader = 5,
        [Display(Name = "Bureau")]
        Bureau = 6
    }
}
