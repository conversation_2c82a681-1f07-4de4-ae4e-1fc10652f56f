using System.ComponentModel.DataAnnotations;

namespace Sacrra.Membership.Database.Enums
{
    public enum IndustryClassifications
    {
        [Display(Name = "Furniture Retail")]
        FurnitureRetail = 1,
        [Display(Name = "Insurance")]
        Insurance = 2,
        [Display(Name = "Retail Apparel")]
        RetailApparel = 3,
        [Display(Name = "Secured")]
        Secured = 4,
        [Display(Name = "Unsecured")]
        Unsecured = 5,
        [Display(Name = "Other")]
        Other = 7,
        [Display(Name = "Telecommunication")]
        Telecommunication = 8,
        [Display(Name = "Secured Banks")]
        SecuredBanks = 9,
        [Display(Name = "Other - Debt Collectors or Debt Purchasers")]
        OtherDebtCollectorsOrDebtPurchasers = 10,
        [Display(Name = "Secured vehicle finance")]
        SecuredVehicleFinance = 11,
        [Display(Name = "Subscription")]
        Subscription = 12,
        [Display(Name = "Secured other financial institutions")]
        SecuredOtherFinancialInstitutions = 13
    }
}
