using System.ComponentModel.DataAnnotations;

namespace Sacrra.Membership.Database.Enums
{
    public enum NcrReportingPrimaryBusinessClassifications
    {
        [Display(Name = "7.2 a Banks")]
        Banks = 1,
        [Display(Name = "7,2 b Retailers")]
        Retailers = 2,
        [Display(Name = "7.2 c VAF & Other Financial Institutions")]
        VAFAndOtherFinancialInstitutions = 3,
        [Display(Name = "7.2 d Microlenders")]
        MicroLenders = 4,
        [Display(Name = "7.2 e Telecommunications")]
        Telecommunications = 5,
        [Display(Name = "7.2 f Utility Companies")]
        UtilityCompanies = 6,
        [Display(Name = "7.2 g Insurance")]
        Insurance = 7,
        [Display(Name = "7.2 h On sellers")]
        OnSellers = 8,
        [Display(Name = "7.2 i Employers or Employment Agencies")]
        EmployersOrEmploymentAgencies = 9,
        [Display(Name = "7.2 j Debt Collectors")]
        DebtCollectors = 10,
        [Display(Name = "7.2 k Debt Counsellors")]
        DebtCounsellors = 11,
        [Display(Name = "7.2 l Other")]
        Other = 12
    }
}
