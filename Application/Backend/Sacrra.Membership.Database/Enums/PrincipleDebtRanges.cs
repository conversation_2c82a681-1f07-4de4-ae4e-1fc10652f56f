using System.ComponentModel.DataAnnotations;

namespace Sacrra.Membership.Database.Enums
{
    public enum PrincipleDebtRanges
    {
        [Display(Name = "R15,000,000,000+")]
        N1 = 9,
        [Display(Name = "R5,000,000,000 - R14,999,999,999")]
        N2 = 8,
        [Display(Name = "R1,000,000,000 - R4,999,999,999")]
        N3 = 7,
        [Display(Name = "R100,000,000 - R999,999,999")]
        N4 = 6,
        [Display(Name = "R5,000,000 - R99,999,999")]
        N5 = 5,
        [Display(Name = "R1,000,000 - R4,999,999")]
        N6 = 4,
        [Display(Name = "R500,000 - R999,999")]
        N7 = 3,
        [Display(Name = "R250,000 - R499,999")]
        N8 = 2,
        [Display(Name = "Less than R250,000")]
        N9 = 1
    }
}
