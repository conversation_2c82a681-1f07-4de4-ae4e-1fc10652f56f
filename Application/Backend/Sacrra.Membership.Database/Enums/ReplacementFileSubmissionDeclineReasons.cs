using System.ComponentModel.DataAnnotations;

namespace Sacrra.Membership.Database.Enums
{
    public enum ReplacementFileSubmissionDeclineReasons
    {
        [Display(Name = "Data in file is incorrect")]
        DataFileIncorrect = 1,

        [Display(Name = "​File was not validated through the DMA")]
        FileNotValidatedThroughDMA = 2,

        [Display(Name = "​File sent in error")]
        FileSentInError = 3,

        [Display(Name = "​File already successfully processed by the bureaus")]
        FileAlreadyProcessedByBureaus = 4
    }
}
