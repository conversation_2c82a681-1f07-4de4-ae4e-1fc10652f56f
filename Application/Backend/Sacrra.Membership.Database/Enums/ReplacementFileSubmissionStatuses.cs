using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace Sacrra.Membership.Database.Enums
{
    public enum ReplacementFileSubmissionStatuses
    {
        [Display(Name = "File Submission Requested")]
        Requested = 1,

        [Display(Name = "File Submission Request Approved")]
        Approved = 2,

        [Display(Name = "File Submission Request Cancelled")]
        Cancelled = 3,

        [Display(Name = "File Submission Request Declined")]
        Declined = 4,

        [Display(Name = "File Pending Submission")]
        PendingSubmission = 5,

        [Display(Name = "File Submitted to DTH")]
        Submitted = 6
    }
}
