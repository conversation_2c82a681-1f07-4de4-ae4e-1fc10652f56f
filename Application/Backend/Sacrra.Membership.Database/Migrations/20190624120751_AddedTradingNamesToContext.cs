using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class AddedTradingNamesToContext : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TradingName_Members_MemberId",
                table: "TradingName");

            migrationBuilder.DropPrimaryKey(
                name: "PK_TradingName",
                table: "TradingName");

            migrationBuilder.RenameTable(
                name: "TradingName",
                newName: "TradingNames");

            migrationBuilder.RenameIndex(
                name: "IX_TradingName_MemberId",
                table: "TradingNames",
                newName: "IX_TradingNames_MemberId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_TradingNames",
                table: "TradingNames",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_TradingNames_Members_MemberId",
                table: "TradingNames",
                column: "MemberId",
                principalTable: "Members",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TradingNames_Members_MemberId",
                table: "TradingNames");

            migrationBuilder.DropPrimaryKey(
                name: "PK_TradingNames",
                table: "TradingNames");

            migrationBuilder.RenameTable(
                name: "TradingNames",
                newName: "TradingName");

            migrationBuilder.RenameIndex(
                name: "IX_TradingNames_MemberId",
                table: "TradingName",
                newName: "IX_TradingName_MemberId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_TradingName",
                table: "TradingName",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_TradingName_Members_MemberId",
                table: "TradingName",
                column: "MemberId",
                principalTable: "Members",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
