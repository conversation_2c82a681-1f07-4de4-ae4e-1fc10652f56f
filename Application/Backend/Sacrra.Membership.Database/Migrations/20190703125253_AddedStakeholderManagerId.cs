using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class AddedStakeholderManagerId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "StakeholderManagerId",
                table: "Members",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Members_StakeholderManagerId",
                table: "Members",
                column: "StakeholderManagerId");

            migrationBuilder.AddForeignKey(
                name: "FK_Members_Users_StakeholderManagerId",
                table: "Members",
                column: "StakeholderManagerId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Members_Users_StakeholderManagerId",
                table: "Members");

            migrationBuilder.DropIndex(
                name: "IX_Members_StakeholderManagerId",
                table: "Members");

            migrationBuilder.DropColumn(
                name: "StakeholderManagerId",
                table: "Members");
        }
    }
}
