using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class UpdatedSRNStatusId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SRNs_SRNStatuses_SRNStatusId",
                table: "SRNs");

            migrationBuilder.AlterColumn<int>(
                name: "SRNStatusId",
                table: "SRNs",
                nullable: false,
                oldClrType: typeof(int),
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_SRNs_SRNStatuses_SRNStatusId",
                table: "SRNs",
                column: "SRNStatusId",
                principalTable: "SRNStatuses",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SRNs_SRNStatuses_SRNStatusId",
                table: "SRNs");

            migrationBuilder.AlterColumn<int>(
                name: "SRNStatusId",
                table: "SRNs",
                nullable: true,
                oldClrType: typeof(int));

            migrationBuilder.AddForeignKey(
                name: "FK_SRNs_SRNStatuses_SRNStatusId",
                table: "SRNs",
                column: "SRNStatusId",
                principalTable: "SRNStatuses",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
