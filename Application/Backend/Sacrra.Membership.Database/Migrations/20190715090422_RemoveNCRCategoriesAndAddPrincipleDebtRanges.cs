using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class RemoveNCRCategoriesAndAddPrincipleDebtRanges : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "NcrCategoryId",
                table: "PartialMembers");

            migrationBuilder.DropColumn(
                name: "PrincipleDebtValue",
                table: "PartialMembers");

            migrationBuilder.DropColumn(
                name: "NcrCategoryId",
                table: "Members");

            migrationBuilder.DropColumn(
                name: "PrincipleDebtValue",
                table: "Members");

            migrationBuilder.AddColumn<string>(
                name: "NcrCategory",
                table: "PartialMembers",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "PrincipleDebtRangeId",
                table: "PartialMembers",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "NcrCategory",
                table: "Members",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "PrincipleDebtRangeId",
                table: "Members",
                nullable: false,
                defaultValue: 0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "NcrCategory",
                table: "PartialMembers");

            migrationBuilder.DropColumn(
                name: "PrincipleDebtRangeId",
                table: "PartialMembers");

            migrationBuilder.DropColumn(
                name: "NcrCategory",
                table: "Members");

            migrationBuilder.DropColumn(
                name: "PrincipleDebtRangeId",
                table: "Members");

            migrationBuilder.AddColumn<int>(
                name: "NcrCategoryId",
                table: "PartialMembers",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "PrincipleDebtValue",
                table: "PartialMembers",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "NcrCategoryId",
                table: "Members",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "PrincipleDebtValue",
                table: "Members",
                nullable: false,
                defaultValue: 0);
        }
    }
}
