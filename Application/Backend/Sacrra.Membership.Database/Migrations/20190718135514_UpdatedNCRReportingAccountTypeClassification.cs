using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class UpdatedNCRReportingAccountTypeClassification : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SRNs_NCRReportingAccountTypeClassification_NCRReportingAccountTypeClassificationId",
                table: "SRNs");

            migrationBuilder.DropPrimaryKey(
                name: "PK_NCRReportingAccountTypeClassification",
                table: "NCRReportingAccountTypeClassification");

            migrationBuilder.RenameTable(
                name: "NCRReportingAccountTypeClassification",
                newName: "NCRReportingAccountTypeClassifications");

            migrationBuilder.AddPrimaryKey(
                name: "PK_NCRReportingAccountTypeClassifications",
                table: "NCRReportingAccountTypeClassifications",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_SRNs_NCRReportingAccountTypeClassifications_NCRReportingAccountTypeClassificationId",
                table: "SRNs",
                column: "NCRReportingAccountTypeClassificationId",
                principalTable: "NCRReportingAccountTypeClassifications",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SRNs_NCRReportingAccountTypeClassifications_NCRReportingAccountTypeClassificationId",
                table: "SRNs");

            migrationBuilder.DropPrimaryKey(
                name: "PK_NCRReportingAccountTypeClassifications",
                table: "NCRReportingAccountTypeClassifications");

            migrationBuilder.RenameTable(
                name: "NCRReportingAccountTypeClassifications",
                newName: "NCRReportingAccountTypeClassification");

            migrationBuilder.AddPrimaryKey(
                name: "PK_NCRReportingAccountTypeClassification",
                table: "NCRReportingAccountTypeClassification",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_SRNs_NCRReportingAccountTypeClassification_NCRReportingAccountTypeClassificationId",
                table: "SRNs",
                column: "NCRReportingAccountTypeClassificationId",
                principalTable: "NCRReportingAccountTypeClassification",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
