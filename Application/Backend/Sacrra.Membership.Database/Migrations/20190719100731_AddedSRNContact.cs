using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class AddedSRNContact : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "SRNContacts",
                columns: table => new
                {
                    Id = table.Column<int>(nullable: false)
                        .Annotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn),
                    FirstName = table.Column<string>(nullable: true),
                    Surname = table.Column<string>(nullable: true),
                    JobTitle = table.Column<string>(nullable: true),
                    OfficeTelNumber = table.Column<string>(nullable: true),
                    CellNumber = table.Column<string>(nullable: true),
                    Email = table.Column<string>(nullable: true),
                    SRNId = table.Column<int>(nullable: false),
                    ContactTypeId = table.Column<int>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SRNContacts", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SRNContacts_ContactTypes_ContactTypeId",
                        column: x => x.ContactTypeId,
                        principalTable: "ContactTypes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_SRNContacts_SRNs_SRNId",
                        column: x => x.SRNId,
                        principalTable: "SRNs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_SRNContacts_ContactTypeId",
                table: "SRNContacts",
                column: "ContactTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_SRNContacts_SRNId",
                table: "SRNContacts",
                column: "SRNId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SRNContacts");
        }
    }
}
