using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class AddedSRNIdToBranchLocation : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SRNs_BranchLocations_BranchLocationId",
                table: "SRNs");

            migrationBuilder.DropIndex(
                name: "IX_SRNs_BranchLocationId",
                table: "SRNs");

            migrationBuilder.DropColumn(
                name: "BranchLocationId",
                table: "SRNs");

            migrationBuilder.AddColumn<int>(
                name: "SRNId",
                table: "BranchLocations",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_BranchLocations_SRNId",
                table: "BranchLocations",
                column: "SRNId");

            migrationBuilder.AddForeignKey(
                name: "FK_BranchLocations_SRNs_SRNId",
                table: "BranchLocations",
                column: "SRNId",
                principalTable: "SRNs",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_BranchLocations_SRNs_SRNId",
                table: "BranchLocations");

            migrationBuilder.DropIndex(
                name: "IX_BranchLocations_SRNId",
                table: "BranchLocations");

            migrationBuilder.DropColumn(
                name: "SRNId",
                table: "BranchLocations");

            migrationBuilder.AddColumn<int>(
                name: "BranchLocationId",
                table: "SRNs",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_SRNs_BranchLocationId",
                table: "SRNs",
                column: "BranchLocationId");

            migrationBuilder.AddForeignKey(
                name: "FK_SRNs_BranchLocations_BranchLocationId",
                table: "SRNs",
                column: "BranchLocationId",
                principalTable: "BranchLocations",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
