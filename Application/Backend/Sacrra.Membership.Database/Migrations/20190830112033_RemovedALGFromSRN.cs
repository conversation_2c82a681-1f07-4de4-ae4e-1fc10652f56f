using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class RemovedALGFromSRN : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SRNs_ALGs_ALGId",
                table: "SRNs");

            migrationBuilder.DropIndex(
                name: "IX_SRNs_ALGId",
                table: "SRNs");

            migrationBuilder.DropColumn(
                name: "ALGId",
                table: "SRNs");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "ALGId",
                table: "SRNs",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_SRNs_ALGId",
                table: "SRNs",
                column: "ALGId");

            migrationBuilder.AddForeignKey(
                name: "FK_SRNs_ALGs_ALGId",
                table: "SRNs",
                column: "ALGId",
                principalTable: "ALGs",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
