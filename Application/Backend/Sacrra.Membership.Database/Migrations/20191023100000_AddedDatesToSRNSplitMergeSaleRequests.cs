using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class AddedDatesToSRNSplitMergeSaleRequests : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "FileDevelopentEndDate",
                table: "SRNSplitRequests",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FileDevelopmentStartDate",
                table: "SRNSplitRequests",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FileTestEndDate",
                table: "SRNSplitRequests",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FileTestStartDate",
                table: "SRNSplitRequests",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "GoLiveDate",
                table: "SRNSplitRequests",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FileDevelopentEndDate",
                table: "SRNSaleRequests",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FileDevelopmentStartDate",
                table: "SRNSaleRequests",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FileTestEndDate",
                table: "SRNSaleRequests",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FileTestStartDate",
                table: "SRNSaleRequests",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "GoLiveDate",
                table: "SRNSaleRequests",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FileDevelopentEndDate",
                table: "SRNMergeRequests",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FileDevelopmentStartDate",
                table: "SRNMergeRequests",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FileTestEndDate",
                table: "SRNMergeRequests",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FileTestStartDate",
                table: "SRNMergeRequests",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "GoLiveDate",
                table: "SRNMergeRequests",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "FileDevelopentEndDate",
                table: "SRNSplitRequests");

            migrationBuilder.DropColumn(
                name: "FileDevelopmentStartDate",
                table: "SRNSplitRequests");

            migrationBuilder.DropColumn(
                name: "FileTestEndDate",
                table: "SRNSplitRequests");

            migrationBuilder.DropColumn(
                name: "FileTestStartDate",
                table: "SRNSplitRequests");

            migrationBuilder.DropColumn(
                name: "GoLiveDate",
                table: "SRNSplitRequests");

            migrationBuilder.DropColumn(
                name: "FileDevelopentEndDate",
                table: "SRNSaleRequests");

            migrationBuilder.DropColumn(
                name: "FileDevelopmentStartDate",
                table: "SRNSaleRequests");

            migrationBuilder.DropColumn(
                name: "FileTestEndDate",
                table: "SRNSaleRequests");

            migrationBuilder.DropColumn(
                name: "FileTestStartDate",
                table: "SRNSaleRequests");

            migrationBuilder.DropColumn(
                name: "GoLiveDate",
                table: "SRNSaleRequests");

            migrationBuilder.DropColumn(
                name: "FileDevelopentEndDate",
                table: "SRNMergeRequests");

            migrationBuilder.DropColumn(
                name: "FileDevelopmentStartDate",
                table: "SRNMergeRequests");

            migrationBuilder.DropColumn(
                name: "FileTestEndDate",
                table: "SRNMergeRequests");

            migrationBuilder.DropColumn(
                name: "FileTestStartDate",
                table: "SRNMergeRequests");

            migrationBuilder.DropColumn(
                name: "GoLiveDate",
                table: "SRNMergeRequests");
        }
    }
}
