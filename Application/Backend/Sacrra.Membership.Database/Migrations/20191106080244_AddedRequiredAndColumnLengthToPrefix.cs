using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class AddedRequiredAndColumnLengthToPrefix : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "Prefix",
                table: "LastGeneratedSRNNumber",
                type: "VARCHAR(2)",
                nullable: false,
                oldClrType: typeof(string),
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Exclusions",
                table: "LastGeneratedSRNNumber",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Exclusions",
                table: "LastGeneratedSRNNumber");

            migrationBuilder.AlterColumn<string>(
                name: "Prefix",
                table: "LastGeneratedSRNNumber",
                nullable: true,
                oldClrType: typeof(string),
                oldType: "VARCHAR(2)");
        }
    }
}
