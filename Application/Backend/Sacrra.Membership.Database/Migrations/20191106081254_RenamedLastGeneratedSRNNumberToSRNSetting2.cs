using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class RenamedLastGeneratedSRNNumberToSRNSetting2 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_LastGeneratedSRNNumber",
                table: "LastGeneratedSRNNumber");

            migrationBuilder.RenameTable(
                name: "LastGeneratedSRNNumber",
                newName: "SRNSettings");

            migrationBuilder.AddPrimaryKey(
                name: "PK_SRNSettings",
                table: "SRNSettings",
                column: "Id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_SRNSettings",
                table: "SRNSettings");

            migrationBuilder.RenameTable(
                name: "SRNSettings",
                newName: "LastGeneratedSRNNumber");

            migrationBuilder.AddPrimaryKey(
                name: "<PERSON>K_LastGeneratedSRNNumber",
                table: "LastGeneratedSRNNumber",
                column: "Id");
        }
    }
}
