using System;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class AddedDWExceptionModel : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "DWExceptions",
                columns: table => new
                {
                    Id = table.Column<int>(nullable: false)
                        .Annotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn),
                    FctWarehouseExceptionID = table.Column<long>(nullable: false),
                    Exception = table.Column<string>(nullable: true),
                    ExceptionDesc = table.Column<string>(nullable: true),
                    ExceptionCategory = table.Column<string>(nullable: true),
                    ExceptionDateTime = table.Column<DateTime>(nullable: false),
                    SRNNumber = table.Column<string>(nullable: true),
                    ExceptionStatus = table.Column<string>(nullable: true),
                    IsSentToPortal = table.Column<string>(nullable: true),
                    DatePulled = table.Column<DateTime>(nullable: false),
                    CamundaTaskId = table.Column<string>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DWExceptions", x => x.Id);
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DWExceptions");
        }
    }
}
