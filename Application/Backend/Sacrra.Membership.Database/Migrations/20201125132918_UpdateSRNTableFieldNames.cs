using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class UpdateSRNTableFieldNames : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "FileType",
                table: "SRNs");

            migrationBuilder.DropColumn(
                name: "StatusType",
                table: "SRNs");

            migrationBuilder.AddColumn<int>(
                name: "FileTypeId",
                table: "SRNs",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "StatusTypeId",
                table: "SRNs",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "FileTypeId",
                table: "SRNs");

            migrationBuilder.DropColumn(
                name: "StatusTypeId",
                table: "SRNs");

            migrationBuilder.AddColumn<int>(
                name: "FileType",
                table: "SRNs",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "StatusType",
                table: "SRNs",
                nullable: true);
        }
    }
}
