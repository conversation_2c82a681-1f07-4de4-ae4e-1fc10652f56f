using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class AddedSRNStatusReasonSRNStatusLinkingModel : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SRNStatusReasons_SRNStatuses_SRNStatusId",
                table: "SRNStatusReasons");

            migrationBuilder.DropIndex(
                name: "IX_SRNStatusReasons_SRNStatusId",
                table: "SRNStatusReasons");

            migrationBuilder.DropColumn(
                name: "SRNStatusId",
                table: "SRNStatusReasons");

            migrationBuilder.CreateTable(
                name: "SRNStatusReasonSRNStatusLink",
                columns: table => new
                {
                    Id = table.Column<int>(nullable: false)
                        .Annotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn),
                    SRNStatusReasonId = table.Column<int>(nullable: false),
                    SRNStatusId = table.Column<int>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SRNStatusReasonSRNStatusLink", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SRNStatusReasonSRNStatusLink_SRNStatuses_SRNStatusId",
                        column: x => x.SRNStatusId,
                        principalTable: "SRNStatuses",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_SRNStatusReasonSRNStatusLink_SRNStatusReasons_SRNStatusReasonId",
                        column: x => x.SRNStatusReasonId,
                        principalTable: "SRNStatusReasons",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateIndex(
                name: "IX_SRNStatusReasonSRNStatusLink_SRNStatusId",
                table: "SRNStatusReasonSRNStatusLink",
                column: "SRNStatusId");

            migrationBuilder.CreateIndex(
                name: "IX_SRNStatusReasonSRNStatusLink_SRNStatusReasonId",
                table: "SRNStatusReasonSRNStatusLink",
                column: "SRNStatusReasonId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SRNStatusReasonSRNStatusLink");

            migrationBuilder.AddColumn<int>(
                name: "SRNStatusId",
                table: "SRNStatusReasons",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_SRNStatusReasons_SRNStatusId",
                table: "SRNStatusReasons",
                column: "SRNStatusId");

            migrationBuilder.AddForeignKey(
                name: "FK_SRNStatusReasons_SRNStatuses_SRNStatusId",
                table: "SRNStatusReasons",
                column: "SRNStatusId",
                principalTable: "SRNStatuses",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
