using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class AddedSRNFileStatus : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "SRNFileStatusId",
                table: "SRNStatusUpdateHistory",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "SRNFileStatuses",
                columns: table => new
                {
                    Id = table.Column<int>(nullable: false)
                        .Annotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn),
                    Name = table.Column<string>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SRNFileStatuses", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_SRNStatusUpdateHistory_SRNFileStatusId",
                table: "SRNStatusUpdateHistory",
                column: "SRNFileStatusId");

            migrationBuilder.AddForeignKey(
                name: "FK_SRNStatusUpdateHistory_SRNFileStatuses_SRNFileStatusId",
                table: "SRNStatusUpdateHistory",
                column: "SRNFileStatusId",
                principalTable: "SRNFileStatuses",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_SRNStatusUpdateHistory_SRNFileStatuses_SRNFileStatusId",
                table: "SRNStatusUpdateHistory");

            migrationBuilder.DropTable(
                name: "SRNFileStatuses");

            migrationBuilder.DropIndex(
                name: "IX_SRNStatusUpdateHistory_SRNFileStatusId",
                table: "SRNStatusUpdateHistory");

            migrationBuilder.DropColumn(
                name: "SRNFileStatusId",
                table: "SRNStatusUpdateHistory");
        }
    }
}
