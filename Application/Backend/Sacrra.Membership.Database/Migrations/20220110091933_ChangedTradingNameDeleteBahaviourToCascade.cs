using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class ChangedTradingNameDeleteBahaviourToCascade : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TradingNames_Members_MemberId",
                table: "TradingNames");

            migrationBuilder.AddForeignKey(
                name: "FK_TradingNames_Members_MemberId",
                table: "TradingNames",
                column: "MemberId",
                principalTable: "Members",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TradingNames_Members_MemberId",
                table: "TradingNames");

            migrationBuilder.AddForeignKey(
                name: "FK_TradingNames_Members_MemberId",
                table: "TradingNames",
                column: "MemberId",
                principalTable: "Members",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
