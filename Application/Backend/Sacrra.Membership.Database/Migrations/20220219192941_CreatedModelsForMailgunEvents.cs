using Microsoft.EntityFrameworkCore.Migrations;

namespace Sacrra.Membership.Database.Migrations
{
    public partial class CreatedModelsForMailgunEvents : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "Mailgun");

            migrationBuilder.CreateTable(
                name: "MailEvents",
                schema: "Mailgun",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1")
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MailEvents", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MailItems",
                schema: "Mailgun",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MailEventId = table.Column<int>(type: "int", nullable: false),
                    Severity = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    RecipientDomain = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    UniqueId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Reason = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LogLevel = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Timestamp = table.Column<double>(type: "float", nullable: false),
                    Recipient = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Event = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MailItems", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MailItems_MailEvents_MailEventId",
                        column: x => x.MailEventId,
                        principalSchema: "Mailgun",
                        principalTable: "MailEvents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "MailPagings",
                schema: "Mailgun",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MailEventId = table.Column<int>(type: "int", nullable: false),
                    Previous = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    First = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Last = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Next = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MailPagings", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MailPagings_MailEvents_MailEventId",
                        column: x => x.MailEventId,
                        principalSchema: "Mailgun",
                        principalTable: "MailEvents",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MailDeliveryStatuses",
                schema: "Mailgun",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MailItemId = table.Column<int>(type: "int", nullable: false),
                    AttemptNo = table.Column<int>(type: "int", nullable: false),
                    Message = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Code = table.Column<int>(type: "int", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    SessionSeconds = table.Column<double>(type: "float", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MailDeliveryStatuses", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MailDeliveryStatuses_MailItems_MailItemId",
                        column: x => x.MailItemId,
                        principalSchema: "Mailgun",
                        principalTable: "MailItems",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MailEnvelops",
                schema: "Mailgun",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MailItemId = table.Column<int>(type: "int", nullable: false),
                    Transport = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Sender = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    SendingIp = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Targets = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MailEnvelops", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MailEnvelops_MailItems_MailItemId",
                        column: x => x.MailItemId,
                        principalSchema: "Mailgun",
                        principalTable: "MailItems",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MailFlags",
                schema: "Mailgun",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MailItemId = table.Column<int>(type: "int", nullable: false),
                    IsRouted = table.Column<bool>(type: "bit", nullable: false),
                    IsAuthenticated = table.Column<bool>(type: "bit", nullable: false),
                    IsSystemTest = table.Column<bool>(type: "bit", nullable: false),
                    IsTestMode = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MailFlags", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MailFlags_MailItems_MailItemId",
                        column: x => x.MailItemId,
                        principalSchema: "Mailgun",
                        principalTable: "MailItems",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MailgunMessages",
                schema: "Mailgun",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MailItemId = table.Column<int>(type: "int", nullable: false),
                    Size = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MailgunMessages", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MailgunMessages_MailItems_MailItemId",
                        column: x => x.MailItemId,
                        principalSchema: "Mailgun",
                        principalTable: "MailItems",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MailStorages",
                schema: "Mailgun",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MailItemId = table.Column<int>(type: "int", nullable: false),
                    Url = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Key = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MailStorages", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MailStorages_MailItems_MailItemId",
                        column: x => x.MailItemId,
                        principalSchema: "Mailgun",
                        principalTable: "MailItems",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MailAttachments",
                schema: "Mailgun",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MailgunMessageId = table.Column<int>(type: "int", nullable: false),
                    Size = table.Column<int>(type: "int", nullable: false),
                    URL = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ContentType = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MailAttachments", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MailAttachments_MailgunMessages_MailgunMessageId",
                        column: x => x.MailgunMessageId,
                        principalSchema: "Mailgun",
                        principalTable: "MailgunMessages",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MailHeaders",
                schema: "Mailgun",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    MailgunMessageId = table.Column<int>(type: "int", nullable: false),
                    To = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    MessageId = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    From = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Subject = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MailHeaders", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MailHeaders_MailgunMessages_MailgunMessageId",
                        column: x => x.MailgunMessageId,
                        principalSchema: "Mailgun",
                        principalTable: "MailgunMessages",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_MailAttachments_MailgunMessageId",
                schema: "Mailgun",
                table: "MailAttachments",
                column: "MailgunMessageId");

            migrationBuilder.CreateIndex(
                name: "IX_MailDeliveryStatuses_MailItemId",
                schema: "Mailgun",
                table: "MailDeliveryStatuses",
                column: "MailItemId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_MailEnvelops_MailItemId",
                schema: "Mailgun",
                table: "MailEnvelops",
                column: "MailItemId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_MailFlags_MailItemId",
                schema: "Mailgun",
                table: "MailFlags",
                column: "MailItemId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_MailgunMessages_MailItemId",
                schema: "Mailgun",
                table: "MailgunMessages",
                column: "MailItemId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_MailHeaders_MailgunMessageId",
                schema: "Mailgun",
                table: "MailHeaders",
                column: "MailgunMessageId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_MailItems_MailEventId",
                schema: "Mailgun",
                table: "MailItems",
                column: "MailEventId");

            migrationBuilder.CreateIndex(
                name: "IX_MailPagings_MailEventId",
                schema: "Mailgun",
                table: "MailPagings",
                column: "MailEventId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_MailStorages_MailItemId",
                schema: "Mailgun",
                table: "MailStorages",
                column: "MailItemId",
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MailAttachments",
                schema: "Mailgun");

            migrationBuilder.DropTable(
                name: "MailDeliveryStatuses",
                schema: "Mailgun");

            migrationBuilder.DropTable(
                name: "MailEnvelops",
                schema: "Mailgun");

            migrationBuilder.DropTable(
                name: "MailFlags",
                schema: "Mailgun");

            migrationBuilder.DropTable(
                name: "MailHeaders",
                schema: "Mailgun");

            migrationBuilder.DropTable(
                name: "MailPagings",
                schema: "Mailgun");

            migrationBuilder.DropTable(
                name: "MailStorages",
                schema: "Mailgun");

            migrationBuilder.DropTable(
                name: "MailgunMessages",
                schema: "Mailgun");

            migrationBuilder.DropTable(
                name: "MailItems",
                schema: "Mailgun");

            migrationBuilder.DropTable(
                name: "MailEvents",
                schema: "Mailgun");
        }
    }
}
