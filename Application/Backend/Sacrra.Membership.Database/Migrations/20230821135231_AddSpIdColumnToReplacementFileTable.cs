using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Sacrra.Membership.Database.Migrations
{
    public partial class AddSpIdColumnToReplacementFileTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ReplacementFileSubmissions_SRNs_SRNId",
                table: "ReplacementFileSubmissions");

            migrationBuilder.AlterColumn<int>(
                name: "SRNId",
                table: "ReplacementFileSubmissions",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddColumn<int>(
                name: "SPId",
                table: "ReplacementFileSubmissions",
                type: "int",
                nullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_ReplacementFileSubmissions_SRNs_SRNId",
                table: "ReplacementFileSubmissions",
                column: "SRNId",
                principalTable: "SRNs",
                principalColumn: "Id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ReplacementFileSubmissions_SRNs_SRNId",
                table: "ReplacementFileSubmissions");

            migrationBuilder.DropColumn(
                name: "SPId",
                table: "ReplacementFileSubmissions");

            migrationBuilder.AlterColumn<int>(
                name: "SRNId",
                table: "ReplacementFileSubmissions",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_ReplacementFileSubmissions_SRNs_SRNId",
                table: "ReplacementFileSubmissions",
                column: "SRNId",
                principalTable: "SRNs",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
