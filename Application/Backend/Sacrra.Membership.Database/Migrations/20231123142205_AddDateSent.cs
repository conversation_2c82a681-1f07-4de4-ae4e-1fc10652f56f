using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Sacrra.Membership.Database.Migrations
{
    public partial class AddDateSent : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "DwDateCreated",
                table: "ReplacementFileSubmissions",
                newName: "DWDateCreated");

            migrationBuilder.AddColumn<DateTime>(
                name: "DateSent",
                table: "EmailAuditLogs",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DateSent",
                table: "EmailAuditLogs");

            migrationBuilder.RenameColumn(
                name: "DWDateCreated",
                table: "ReplacementFileSubmissions",
                newName: "DwDateCreated");
        }
    }
}
