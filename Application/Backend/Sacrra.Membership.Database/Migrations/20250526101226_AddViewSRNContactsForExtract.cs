using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Sacrra.Membership.Database.Migrations
{
    public partial class AddViewSRNContactsForExtract : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
			IF EXISTS
			(
				SELECT TOP (1) 1 FROM sys.all_objects
				WHERE [name] = 'vwSRNContactsForExtract'
			)
			BEGIN
				DROP VIEW [dbo].[vwSRNContactsForExtract]
			END;
			GO

			CREATE VIEW [dbo].[vwSRNContactsForExtract] 
			AS
			SELECT
				srnContacts.[SRNId]
				,srns.[SRNNumber]
				,srnContacts.[FirstName]
				,srnContacts.[Surname]
				,srnContacts.[JobTitle]
				,srnContacts.[OfficeTelNumber]
				,srnContacts.[CellNumber]
				,srnContacts.[Email]
				,srnContacts.[ContactTypeId]
				,contactTypes.[Name] AS ContactType
			FROM SRNContacts srnContacts
			INNER JOIN ContactTypes contactTypes ON srnContacts.[ContactTypeId] = contactTypes.[Id]
			INNER JOIN SRNs srns ON srns.[Id] = srnContacts.[SRNId]
			GO");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
	        migrationBuilder.Sql(@"DROP VIEW [dbo].[vwSRNContactsForExtract];");
        }
    }
}
