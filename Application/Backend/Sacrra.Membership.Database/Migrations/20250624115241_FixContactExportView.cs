using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Sacrra.Membership.Database.Migrations
{
    public partial class FixContactExportView : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
	        migrationBuilder.Sql(@"IF EXISTS
                                    (
                                        SELECT TOP (1) 1 FROM sys.all_objects
                                        WHERE [name] = 'vwSRNContactsForExtract'
                                    )
                                    BEGIN
                                        DROP VIEW [dbo].[vwSRNContactsForExtract]
                                    END;
                                    GO

                                    CREATE VIEW [dbo].[vwSRNContactsForExtract]
                                    AS
										WITH datacontact AS 
										(
											SELECT [Id]
											,[MaxId] = MAX(Id) OVER(PARTITION BY [SRNId])
											,[SRNId]
											,[FirstName]
											,[Surname]
											,[JobTitle]
											,[OfficeTelNumber]
											,[CellNumber]
											,[Email]
											FROM SRNContacts
											WHERE ContactTypeId = 5
										),
										manualamendmentcontact AS 
										(
											SELECT [Id]
											,[MaxId] = MAX(Id) OVER(PARTITION BY [SRNId])
											,[SRNId]
											,[FirstName]
											,[Surname]
											,[JobTitle]
											,[OfficeTelNumber]
											,[CellNumber]
											,[Email]
											FROM SRNContacts
											WHERE ContactTypeId = 6
										),
										compliancecontact AS 
										(
											SELECT  [Id]
											,[MaxId] = MAX(Id) OVER(PARTITION BY [SRNId])
											,[SRNId]
											,[FirstName]
											,[Surname]
											,[JobTitle]
											,[OfficeTelNumber]
											,[CellNumber]
											,[Email]
											FROM SRNContacts
											WHERE ContactTypeId = 4
										),
										dthcontact AS 
										(
											SELECT [Id]
											,[MaxId] = MAX(Id) OVER(PARTITION BY [SRNId])
											,[SRNId]
											,[FirstName]
											,[Surname]
											,[JobTitle]
											,[OfficeTelNumber]
											,[CellNumber]
											,[Email]
											FROM SRNContacts
											WHERE ContactTypeId = 7
										)
										SELECT
											srn.[SRNNumber] AS SRNNumber
											,srn.[MemberId] AS MemberId
											,dc.[FirstName] AS DataContactFirstName
											,dc.[Surname] AS DataContactSurname
											,dc.[JobTitle] AS DataContactJobTitle
											,dc.[OfficeTelNumber] AS DataContactOfficeNumber
											,dc.[CellNumber] AS DataContactCellNumber
											,dc.[Email] AS DataContactEmail

											,ma.[FirstName] AS ManualAmendmentsContactFirstName
											,ma.[Surname] AS ManualAmendmentsContactSurname
											,ma.[JobTitle] AS ManualAmendmentsContactJobTitle
											,ma.[OfficeTelNumber] AS ManualAmendmentsContactOfficeNumber
											,ma.[CellNumber] AS ManualAmendmentsContactCellNumber
											,ma.[Email] AS ManualAmendmentsContactEmail

											,co.[FirstName] AS ComplianceContactFirstName
											,co.[Surname] AS ComplianceContactSurname
											,co.[JobTitle] AS ComplianceContactJobTitle
											,co.[OfficeTelNumber] AS ComplianceContactOfficeNumber
											,co.[CellNumber] AS ComplianceContactCellNumber
											,co.[Email] AS ComplianceContactEmail

											,dthc.[FirstName] AS DTHContactFirstName
											,dthc.[Surname] AS DTHContactSurname
											,dthc.[JobTitle] AS DTHContactJobTitle
											,dthc.[OfficeTelNumber] AS DTHContactOfficeNumber
											,dthc.[CellNumber] AS DTHContactCellNumber
											,dthc.[Email] AS DTHContactEmail

											,cic.[Name] AS CreditInformationClassification
										FROM SRNs srn

										INNER JOIN datacontact dc
										ON srn.[Id] = dc.[SRNId]
										AND dc.[Id] = dc.[MaxId]

										INNER JOIN manualamendmentcontact ma
										ON srn.[Id] = ma.[SRNId]
										AND ma.[Id] = ma.[MaxId]

										INNER JOIN compliancecontact co
										ON srn.[Id] = co.[SRNId]
										AND co.[Id] = co.[MaxId]

										INNER JOIN dthcontact dthc
										ON dc.[SRNId] = dthc.[SRNId]
										AND dthc.[Id] = dthc.[MaxId]

										INNER JOIN CreditInformationClassifications cic
										ON srn.CreditInformationClassificationId = cic.[Id]
									GO");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
	        migrationBuilder.Sql(@"DROP VIEW [dbo].[vwSRNContactsForExtract];");
        }
    }
}
