using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Sacrra.Membership.Database.Models
{
    public class EmailAuditLog
    {
        [Key]
        public int Id { get; set; }
        public string RecipientAddress { get; set; }
        public string RecipientName { get; set; }
        public string Subject { get; set; }
        public string TemplateName { get; set; }
        public string Placeholders { get; set; }
        public string CcAddressList { get; set; }
        public string BccAddressList { get; set; }
        public string CcAddress { get; set; }
        public string BccAddress { get; set; }
        public int? SrnId { get; set; }
        public string Workflow { get; set; }
        public string Reason { get; set; }
        public string RecipientType { get; set; }
        public string MailBody { get; set; }

        public DateTime DateSent { get; set; }

    }
}
