using Sacrra.Membership.Database.Enums;
using Sacrra.Membership.Database.publicShared;
using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Sacrra.Membership.Database.Models
{
    public class SRNMergeRequest : SRNDatesShared
    {
        public int Id { get; set; }

        [ForeignKey("FromSRN")]
        public int FromSRNId { get; set; }
        public virtual SRN FromSRN { get; set; }

        [ForeignKey("ToSRN")]
        public int ToSRNId { get; set; }
        public virtual SRN ToSRN { get; set; }

        public DateTime RequestDate { get; set; }
        public SRNMergeStatus Status { get; set; }
    }
}
