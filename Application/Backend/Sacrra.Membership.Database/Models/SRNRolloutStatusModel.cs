using System;
using System.ComponentModel.DataAnnotations;

namespace Sacrra.Membership.Business.DTOs
{
    public class SRNRolloutStatusModel
    {
        public int Id { get; set; }
        public string FileType { get; set; }
        public string RolloutStatus { get; set; }
        public string SRNStatusReason { get; set; }
        public string Comment { get; set; }

        #region Member Details
        public string RegisteredName { get; set; }
        public string RegisteredNumber { get; set; }
        public string IndustryClassification { get; set; }
        public string StakeholderManager { get; set; }
        #endregion

        #region SRN Details
        public string SRNNumber { get; set; }
        public string TradingName { get; set; }
        public string SPNumber { get; set; }
        public string ALGLeader { get; set; }
        public string StatusLastUpdatedAt { get; set; }
        public string SignoffDate { get; set; }
        public string CreditInformationClassification { get; set; }
        #endregion

        #region Daily File Dates
        [Display(Name = "Daily File Development Start Date")]
        public DateTime? DailyFileDevelopmentStartDate { get; set; }

        [Display(Name = "Daily File Development End Date")]
        public DateTime? DailyFileDevelopmentEndDate { get; set; }

        [Display(Name = "Daily File Test Start Date")]
        public DateTime? DailyFileTestStartDate { get; set; }

        [Display(Name = "Daily File Test End Date")]
        public DateTime? DailyFileTestEndDate { get; set; }

        [Display(Name = "Daily File Go Live Date")]
        public DateTime? DailyFileGoLiveDate { get; set; }

        #endregion

        #region Monthly File Dates

        [Display(Name = "Monthly File Development Start Date")]
        public DateTime? MonthlyFileDevelopmentStartDate { get; set; }

        [Display(Name = "Monthly File Development End Date")]
        public DateTime? MonthlyFileDevelopmentEndDate { get; set; }

        [Display(Name = "Monthly File Test Start Date")]
        public DateTime? MonthlyFileTestStartDate { get; set; }

        [Display(Name = "Monthly File Test End Date")]
        public DateTime? MonthlyFileTestEndDate { get; set; }

        [Display(Name = "Monthly File Go Live Date")]
        public DateTime? MonthlyFileGoLiveDate { get; set; }

        #endregion
    }
}
