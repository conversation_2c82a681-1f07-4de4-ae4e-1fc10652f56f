using Microsoft.EntityFrameworkCore;

namespace Sacrra.Membership.Database.Models;

[Keyless]
public class vwSRNManualAmendmentsContacts
{
    public string SRNNumber { get; set; }
    public int MemberId { get; set; }
    public int? ALGLeaderId { get; set; }
    public string ManualAmendmentsContactFirstName { get; set; }
    public string ManualAmendmentsContactSurname { get; set; }
    public string ManualAmendmentsContactEmail { get; set; }
    public string ManualAmendmentsContactJobTitle { get; set; }
    public string ManualAmendmentsContactCellNumber { get; set; }
    public string ManualAmendmentsContactOfficeNumber { get; set; }
}