using System;
using Microsoft.EntityFrameworkCore;

namespace Sacrra.Membership.Database.Models;

[Keyless]
public class vwSRNSummaryExtract
{
    public string CompanyRegistrationNumber { get; set; }
    public string MemberName { get; set; }
    public int MemberId { get; set; }
    public string Status { get; set; }
    public DateTime? StatusDate { get; set; }
    public string StatusReason { get; set; }
    public string StatusComment { get; set; }
    public DateTime? LastSubmissionDate { get; set; }
    public string SRN { get; set; }
    public string IsLatestHistory { get; set; }
    public string SPNumber { get; set; }
    public string SRNDisplayName { get; set; }
    public string CreditInformationClassification { get; set; }
    public DateTime? SRNCreationDate { get; set; }
    public string PortfolioManager { get; set; }
    public string AccountType { get; set; }
    public string NCRReportingAccountTypeClassification { get; set; }
    public int BillingCycleDay { get; set; }
    public string SRNALGLeader { get; set; }
    public int? SRNALGLeaderId { get; set; }
    public string LoanManagementSystemVendor { get; set; }
    public string BranchLocations { get; set; }
    public int NumberMonthlyRecords { get; set; }
    public DateTime? BureauClosureDate { get; set; }
    public string BureauClosureInstruction { get; set; }
    public string ThirdPartyVendor { get; set; }
}