namespace Sacrra.Membership.Database.Views
{

    public partial class Scripts
    {
        public static string vwSRNSummaryAllDetails = @"
CREATE OR ALTER VIEW [dbo].[vwSRNSummaryAllDetails]
AS
WITH latestDaily
AS
(
SELECT 
	 [MaxId] = MAX([Id]) OVER (PARTITION BY [SRNId])
	,[Id]
	,[SRNId]
	,[DailyFileDevelopmentStartDate]
	,[DailyFileDevelopmentEndDate]
	,[DailyFileTestStartDate]
	,[DailyFileTestEndDate]
	,[DailyFileGoLiveDate]
FROM [dbo].[SRNStatusUpdateHistory]
WHERE [FileType] = 1
),
latestMonthly
AS
(
	SELECT 
		 [MaxId] = MAX([Id]) OVER (PARTITION BY [SRNId])
		,[Id]
		,[SRNId]
		,[MonthlyFileDevelopmentStartDate]
		,[MonthlyFileDevelopmentEndDate]
		,[MonthlyFileTestStartDate]
		,[MonthlyFileTestEndDate]
		,[MonthlyFileGoLiveDate]
	FROM [dbo].[SRNStatusUpdateHistory]
	WHERE [FileType] = 2
)
SELECT 
    [i.SRN].[Id], 
    CAST(dai.[DailyFileDevelopmentStartDate] AS DATE) AS DailyFileDevelopmentStartDate, 
    CAST(dai.[DailyFileDevelopmentEndDate] AS DATE) AS DailyFileDevelopmentEndDate, 
    CAST(dai.[DailyFileTestStartDate] AS DATE) AS DailyFileTestStartDate, 
    CAST(dai.[DailyFileTestEndDate] AS DATE) AS DailyFileTestEndDate, 
    CAST(dai.[DailyFileGoLiveDate] AS DATE) AS DailyFileGoLiveDate,
    CAST(mon.[MonthlyFileDevelopmentStartDate] AS DATE) AS MonthlyFileDevelopmentStartDate, 
    CAST(mon.[MonthlyFileDevelopmentEndDate] AS DATE) AS MonthlyFileDevelopmentEndDate, 
    CAST(mon.[MonthlyFileTestStartDate] AS DATE) AS MonthlyFileTestStartDate, 
    CAST(mon.[MonthlyFileTestEndDate] AS DATE) AS MonthlyFileTestEndDate, 
    CAST(mon.[MonthlyFileGoLiveDate] AS DATE) AS MonthlyFileGoLiveDate, 
    CAST([StatusLastUpdatedAt] AS DATE) AS StatusLastUpdatedAt,
    [i.SRN].[SRNNumber], 
    [i.SRN].[TradingName] AS SRNDisplayName,
    CASE [i.SRNStatus].[Name] WHEN 'Test - DTH user info to be updated' THEN 'Test' ELSE [i.SRNStatus].[Name] END AS [SRNStatus],
    [i.SRN.Member].[RegisteredName] AS [MemberName], 
    [i.SRN.Member].[IsSoleProp] AS [IsSoleProprietor],
    [i.SRN.Member].[IsVatRegistrant], 
    [i.SRN.Member].[VatNumber], 
    [i.SRN.Member].[RegisteredNumber] AS MemberRegisteredNumber,
    CAST([i.SRN].[StatusLastUpdatedAt] AS DATE) AS [SRNStatusDate],
    [i.SRN.Member.StakeholderManager].[FirstName] + ' ' + [i.SRN.Member.StakeholderManager].[LastName] AS [StakeholderManager],
    [i.SRN.Member].[HeadOfficePhysicalAddress], 
    [i.SRN.Member].[HeadOfficePostalAddress], 
    [i.SRN.Member].[AnnualTurnover],
    [i.SRN.Member].[IsNcrRegistrant] AS [IsNCRRegistrant], 
    [i.SRN.Member].[NcrcpNumber] AS [NCRCPNumber], 
    [i.SRN.Member].[AnalyticsCompanyName], 
    [i.SRN].[BillingCycleDay] AS BillingCycle,
    [i.SRN].[NumberOfActiveAccounts] AS [NumberOfAccounts],
    [i.SRN].[BureauInstruction], 
    [i.SRN.ALGLeader].[RegisteredName] AS SRNALGLeader, 
    [i.SRN.SPGroup].[SPNumber],
    CASE 
        WHEN [i.SRN].[FileType] = 1 THEN 'Daily' 
        WHEN [i.SRN].[FileType] = 2 THEN 'Monthly'
        WHEN [i.SRN].[FileType] = 3 THEN 'Daily & Monthly'
    END AS FileType,
    CASE WHEN [i.SRN.Member].[IndustryClassificationId] = 1 THEN 'Furniture Retail' 
        WHEN [i.SRN.Member].[IndustryClassificationId] = 2 THEN 'Insurance'
        WHEN [i.SRN.Member].[IndustryClassificationId] = 3 THEN 'Retail Apparel'
        WHEN [i.SRN.Member].[IndustryClassificationId] = 4 THEN 'Secured'
        WHEN [i.SRN.Member].[IndustryClassificationId] = 5 THEN 'Unsecured'
        WHEN [i.SRN.Member].[IndustryClassificationId] = 6 THEN 'Telecoms'
        WHEN [i.SRN.Member].[IndustryClassificationId] = 7 THEN 'Other'
        WHEN [i.SRN.Member].[IndustryClassificationId] = 8 THEN 'Telecommunication'
        WHEN [i.SRN.Member].[IndustryClassificationId] = 9 THEN 'Secured Banks'
        WHEN [i.SRN.Member].[IndustryClassificationId] = 10 THEN 'Other - Debt Collectors or Debt Purchasers'
        WHEN [i.SRN.Member].[IndustryClassificationId] = 11 THEN 'Secured vehicle finance'
        WHEN [i.SRN.Member].[IndustryClassificationId] = 12 THEN 'Subscription'
        WHEN [i.SRN.Member].[IndustryClassificationId] = 13 THEN 'Secured other financial institutions'
    END AS SacrraIndustryClassification,               
    CASE WHEN [i.SRN.Member].[PrincipleDebtRangeId] = 1 THEN 'R15,000,000,000+' 
        WHEN [i.SRN.Member].[PrincipleDebtRangeId] = 2 THEN 'R5,000,000,000 - R14,999,999,999'
        WHEN [i.SRN.Member].[PrincipleDebtRangeId] = 3 THEN 'R1,000,000,000 - R4,999,999,999'
        WHEN [i.SRN.Member].[PrincipleDebtRangeId] = 4 THEN 'R100,000,000 - R999,999,999'
        WHEN [i.SRN.Member].[PrincipleDebtRangeId] = 5 THEN 'R5,000,000 - R99,999,999'
        WHEN [i.SRN.Member].[PrincipleDebtRangeId] = 6 THEN 'R1,000,000 - R4,999,999'
        WHEN [i.SRN.Member].[PrincipleDebtRangeId] = 7 THEN 'R500,000 - R999,999'
        WHEN [i.SRN.Member].[PrincipleDebtRangeId] = 8 THEN 'R250,000 - R499,999'
        WHEN [i.SRN.Member].[PrincipleDebtRangeId] = 9 THEN 'Less than R250,000'
    END AS PrincipalDebtRange,
    CASE 
        WHEN [i.SRN.Member].[NcrReportingPrimaryBusinessClassificationId] = 1 THEN '7.2 a Banks' 
        WHEN [i.SRN.Member].[NcrReportingPrimaryBusinessClassificationId] = 2 THEN '7,2 b Retailers'
        WHEN [i.SRN.Member].[NcrReportingPrimaryBusinessClassificationId] = 3 THEN '7.2 c VAF & Other Financial Institutions'
        WHEN [i.SRN.Member].[NcrReportingPrimaryBusinessClassificationId] = 4 THEN '7.2 d Microlenders'
        WHEN [i.SRN.Member].[NcrReportingPrimaryBusinessClassificationId] = 5 THEN '7.2 eTelecommunications'
        WHEN [i.SRN.Member].[NcrReportingPrimaryBusinessClassificationId] = 5 THEN '7.2 eTelecommunications'
        WHEN [i.SRN.Member].[NcrReportingPrimaryBusinessClassificationId] = 6 THEN '7.2 f Utility Companies'
        WHEN [i.SRN.Member].[NcrReportingPrimaryBusinessClassificationId] = 7 THEN '7.2 g Insurance'
        WHEN [i.SRN.Member].[NcrReportingPrimaryBusinessClassificationId] = 8 THEN '7.2 h On sellers'
        WHEN [i.SRN.Member].[NcrReportingPrimaryBusinessClassificationId] = 9 THEN '7.2 i Employers or Employment Agencies'
        WHEN [i.SRN.Member].[NcrReportingPrimaryBusinessClassificationId] = 10 THEN '7.2 j Debt Collectors'
        WHEN [i.SRN.Member].[NcrReportingPrimaryBusinessClassificationId] = 11 THEN '7.2 k Debt Counsellors'
        WHEN [i.SRN.Member].[NcrReportingPrimaryBusinessClassificationId] = 12 THEN '7.2 l Other'
    END AS NCRReportingPrimaryBusinessClassification,
    MembershipType = 
        CASE 
            WHEN [i.SRN.Member].MembershipTypeId = 1 THEN 'Full Member'
            WHEN [i.SRN.Member].MembershipTypeId = 2 THEN 'Non Member'
            WHEN [i.SRN.Member].MembershipTypeId = 4 THEN 'ALG Client'
            WHEN [i.SRN.Member].MembershipTypeId = 5 THEN 'ALG Leader'
            WHEN [i.SRN.Member].MembershipTypeId = 6 THEN 'Bureau' 
        END,
    StatusReason.[Name] AS SRNStatusReason,
    [i.SRN].Comments,
    LMSV.[Name] AS LoanManagementSystemVendor,
    PB.[RegisteredName] AS PrimaryBureau,
    SB.[RegisteredName] AS SecondaryBureau,
    BL.BranchLocations,
    [AT].[Name] AS AccountType,
    NCRRAC.[Name] AS NCRReportingAccountTypeClassification,
    [i.SRN].[SignoffDate] AS SRNSignoffDate,
    [i.SRN].[CreationDate] AS SRNCreationDate,
    MemberALGLeaders.RegisteredName AS MemberALGLeaders,
    SRNContact.ComplianceContactFirstName,
    SRNContact.ComplianceContactSurname,
    SRNContact.ComplianceContactCellNumber,
    SRNContact.ComplianceContactOfficeNumber,
    SRNContact.ComplianceContactEmail,
    SRNContact.ComplianceContactJobTitle,
    SRNContact.DataContactFirstName,
    SRNContact.DataContactSurname,
    SRNContact.DataContactCellNumber,
    SRNContact.DataContactOfficeNumber,
    SRNContact.DataContactEmail,
    SRNContact.DataContactJobTitle,
    SRNContact.ManualAmendmentsContactFirstName,
    SRNContact.ManualAmendmentsContactSurname,
    SRNContact.ManualAmendmentsContactCellNumber,
    SRNContact.ManualAmendmentsContactOfficeNumber,
    SRNContact.ManualAmendmentsContactEmail,
    SRNContact.ManualAmendmentsContactJobTitle,
    MemberContact.MainContactFirstName,
    MemberContact.MainContactSurname,
    MemberContact.MainContactCellNumber,
    MemberContact.MainContactOfficeNumber,
    MemberContact.MainContactEmail,
    MemberContact.MainContactJobTitle,
    MemberContact.FinancialContactFirstName,
    MemberContact.FinancialContactSurname,
    MemberContact.FinancialContactCellNumber,
    MemberContact.FinancialContactOfficeNumber,
    MemberContact.FinancialContactEmail,
    MemberContact.FinancialContactJobTitle,
    CAST([i.SRN].[AccountStatusDate] AS DATE) AS BureauClosureDate,
    CreditInformationClassification.[Name] AS CreditInformationClassification,
    [i.SRN.Member].[Id] AS MemberID,
    [i.SRN].[ALGLeaderId] AS AlgLeaderID
FROM SRNs AS [i.SRN]
LEFT JOIN latestDaily dai ON dai.[SRNId] = [i.SRN].[Id] AND dai.[Id] = dai.[MaxId]
LEFT JOIN latestMonthly mon ON mon.[SRNId] = [i.SRN].[Id] AND mon.[Id] = mon.[MaxId]
INNER JOIN [Members] AS [i.SRN.Member] ON [i.SRN].[MemberId] = [i.SRN.Member].[Id]
LEFT JOIN [Users] AS [i.SRN.Member.StakeholderManager] ON [i.SRN.Member].[StakeholderManagerId] = [i.SRN.Member.StakeholderManager].[Id]
LEFT JOIN [Members] AS [i.SRN.ALGLeader] ON [i.SRN].[ALGLeaderId] = [i.SRN.ALGLeader].[Id]
LEFT JOIN [SPGroups] AS [i.SRN.SPGroup] ON [i.SRN].[SPGroupId] = [i.SRN.SPGroup].[Id]
LEFT JOIN [LoanManagementSystemVendors] LMSV ON [i.SRN].[LoanManagementSystemVendorId] = LMSV.Id
LEFT JOIN [Members] PB ON [i.SRN.Member].[PrimaryBureauId] = PB.Id
LEFT JOIN [AccountTypes] [AT] ON [i.SRN].[AccountTypeId] = [AT].Id
LEFT JOIN [Members] SB ON [i.SRN.Member].[SecondaryBureauId] = SB.Id
LEFT JOIN
(
    SELECT SRNId, STRING_AGG([Name],', ') BranchLocations 
    FROM [BranchLocations]
    GROUP BY SRNId
) AS BL ON [i.SRN].Id = BL.SRNId
LEFT JOIN [NCRReportingAccountTypeClassifications] NCRRAC ON [i.SRN].[NCRReportingAccountTypeClassificationId] = NCRRAC.Id
LEFT JOIN [SRNStatuses] AS [i.SRNStatus] ON [i.SRN].[SRNStatusId] = [i.SRNStatus].[Id]
LEFT JOIN [SRNStatusReasons] AS StatusReason on [i.SRN].[SRNStatusReasonId] = StatusReason.Id
LEFT JOIN [CreditInformationClassifications] AS CreditInformationClassification ON [i.SRN].[CreditInformationClassificationId] = CreditInformationClassification.Id
LEFT JOIN
(
    SELECT ClientId, STRING_AGG(RegisteredName,', ') RegisteredName 
    FROM ALGClientLeaders ACL
    INNER JOIN Members M ON ACL.LeaderId = M.Id
    GROUP BY ClientId
) AS MemberALGLeaders ON [i.SRN].[MemberId] = MemberALGLeaders.ClientId
LEFT JOIN
(
    SELECT 
        SRNId,
        MIN(CASE WHEN c.ContactTypeId = 4 THEN c.FirstName ELSE NULL END) AS ComplianceContactFirstName,
        MIN(CASE WHEN c.ContactTypeId = 4 THEN c.Surname ELSE NULL END) AS ComplianceContactSurname,
        MIN(CASE WHEN c.ContactTypeId = 4 THEN c.CellNumber ELSE NULL END) AS ComplianceContactCellNumber,
        MIN(CASE WHEN c.ContactTypeId = 4 THEN c.OfficeTelNumber ELSE NULL END) AS ComplianceContactOfficeNumber,
        MIN(CASE WHEN c.ContactTypeId = 4 THEN c.Email ELSE NULL END) AS ComplianceContactEmail,
        MIN(CASE WHEN c.ContactTypeId = 4 THEN c.JobTitle ELSE NULL END) AS ComplianceContactJobTitle,
        MIN(CASE WHEN c.ContactTypeId = 5 THEN c.FirstName ELSE NULL END) AS DataContactFirstName,
        MIN(CASE WHEN c.ContactTypeId = 5 THEN c.Surname ELSE NULL END) AS DataContactSurname,
        MIN(CASE WHEN c.ContactTypeId = 5 THEN c.CellNumber ELSE NULL END) AS DataContactCellNumber,
        MIN(CASE WHEN c.ContactTypeId = 5 THEN c.OfficeTelNumber ELSE NULL END) AS DataContactOfficeNumber,
        MIN(CASE WHEN c.ContactTypeId = 5 THEN c.Email ELSE NULL END) AS DataContactEmail,
        MIN(CASE WHEN c.ContactTypeId = 5 THEN c.JobTitle ELSE NULL END) AS DataContactJobTitle,
        MIN(CASE WHEN c.ContactTypeId = 6 THEN c.FirstName ELSE NULL END) AS ManualAmendmentsContactFirstName,
        MIN(CASE WHEN c.ContactTypeId = 6 THEN c.Surname ELSE NULL END) AS ManualAmendmentsContactSurname,
        MIN(CASE WHEN c.ContactTypeId = 6 THEN c.CellNumber ELSE NULL END) AS ManualAmendmentsContactCellNumber,
        MIN(CASE WHEN c.ContactTypeId = 6 THEN c.OfficeTelNumber ELSE NULL END) AS ManualAmendmentsContactOfficeNumber,
        MIN(CASE WHEN c.ContactTypeId = 6 THEN c.Email ELSE NULL END) AS ManualAmendmentsContactEmail,
        MIN(CASE WHEN c.ContactTypeId = 6 THEN c.JobTitle ELSE NULL END) AS ManualAmendmentsContactJobTitle,
        MIN(CASE WHEN c.ContactTypeId = 7 THEN c.FirstName ELSE NULL END) AS DTHContactFirstName,
        MIN(CASE WHEN c.ContactTypeId = 7 THEN c.Surname ELSE NULL END) AS DTHContactSurname,
        MIN(CASE WHEN c.ContactTypeId = 7 THEN c.CellNumber ELSE NULL END) AS DTHContactCellNumber,
        MIN(CASE WHEN c.ContactTypeId = 7 THEN c.OfficeTelNumber ELSE NULL END) AS DTHContactOfficeNumber,
        MIN(CASE WHEN c.ContactTypeId = 7 THEN c.Email ELSE NULL END) AS DTHContactEmail,
        MIN(CASE WHEN c.ContactTypeId = 7 THEN c.JobTitle ELSE NULL END) AS DTHContactJobTitle
    FROM dbo.SRNContacts c
    GROUP BY SRNId
) AS SRNContact ON [i.SRN].Id = SRNContact.SRNId
LEFT JOIN
(
    SELECT 
        MemberId,
        MIN(CASE WHEN c.ContactTypeId = 1 THEN c.FirstName ELSE NULL END) AS MainContactFirstName,
        MIN(CASE WHEN c.ContactTypeId = 1 THEN c.Surname ELSE NULL END) AS MainContactSurname,
        MIN(CASE WHEN c.ContactTypeId = 1 THEN c.CellNumber ELSE NULL END) AS MainContactCellNumber,
        MIN(CASE WHEN c.ContactTypeId = 1 THEN c.OfficeTelNumber ELSE NULL END) AS MainContactOfficeNumber,
        MIN(CASE WHEN c.ContactTypeId = 1 THEN c.Email ELSE NULL END) AS MainContactEmail,
        MIN(CASE WHEN c.ContactTypeId = 1 THEN c.JobTitle ELSE NULL END) AS MainContactJobTitle,
        MIN(CASE WHEN c.ContactTypeId = 3 THEN c.FirstName ELSE NULL END) AS FinancialContactFirstName,
        MIN(CASE WHEN c.ContactTypeId = 3 THEN c.Surname ELSE NULL END) AS FinancialContactSurname,
        MIN(CASE WHEN c.ContactTypeId = 3 THEN c.CellNumber ELSE NULL END) AS FinancialContactCellNumber,
        MIN(CASE WHEN c.ContactTypeId = 3 THEN c.OfficeTelNumber ELSE NULL END) AS FinancialContactOfficeNumber,
        MIN(CASE WHEN c.ContactTypeId = 3 THEN c.Email ELSE NULL END) AS FinancialContactEmail,
        MIN(CASE WHEN c.ContactTypeId = 3 THEN c.JobTitle ELSE NULL END) AS FinancialContactJobTitle
    FROM dbo.MemberContacts c
    GROUP BY MemberId
) AS MemberContact ON [i.SRN.Member].Id = MemberContact.MemberId
WHERE [i.SRN].[SRNStatusId] <> 7 AND [i.SRNStatus].[Name] != 'Rejected'
        ";
    }
}