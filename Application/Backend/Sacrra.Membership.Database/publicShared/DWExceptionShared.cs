using System;

namespace Sacrra.Membership.Database
{
    public class DWExceptionShared
    {
        public long FctWarehouseExceptionID { get; set; }
        public string Exception { get; set; }
        public string ExceptionDesc { get; set; }
        public string ExceptionCategory { get; set; }
        public DateTime ExceptionDateTime { get; set; }
        public string SRNNumber { get; set; }
        public string ExceptionStatus { get; set; }
        public string IsSentToPortal { get; set; }
        public string SHM { get; set; }
        public DateTime? TransactionDate { get; set; }
        public string ExceptionInfo { get; set; }
        public string BureauName { get; set; }
        public string FileName { get; set; }
    }
}
