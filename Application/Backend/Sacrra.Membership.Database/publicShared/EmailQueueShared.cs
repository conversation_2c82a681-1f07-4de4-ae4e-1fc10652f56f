using Sacrra.Membership.Database.Enums;
using System;
using System.ComponentModel.DataAnnotations;

namespace Sacrra.Membership.Database.publicShared
{
    public class EmailQueueShared
    {
        [Required]
        public string Email { get; set; }
        public int EntityId { get; set; }
        public DateTime? DateSent { get; set; }
        public int RetryCount { get; set; }
        public EmailStatusEnum Status { get; set; }
        public EmailRecipientTypeEnum RecipientType { get; set; }

        public EmailReasonEnum Reason { get; set; }
        public WorkflowEnum? Workflow { get; set; }
        /// <summary>
        /// The date when the email was sent or date when an attempt to send the email was done
        /// </summary>
        public DateTime LastActioned { get; set; }
    }
}
