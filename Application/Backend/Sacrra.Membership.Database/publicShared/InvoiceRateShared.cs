using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Sacrra.Membership.Database
{
    public abstract class InvoiceRateShared
    {
        [Column(TypeName = "VARCHAR(1000)")]
        public string Description { get; set; }
        public DateTime DateFrom { get; set; }
        public DateTime DateTo { get; set; }
        public double Amount { get; set; }
        public bool IsActive { get; set; }
    }
}
