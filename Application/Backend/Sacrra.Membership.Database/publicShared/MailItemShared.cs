using Newtonsoft.Json;

namespace Sacrra.Membership.Database
{
    public abstract class MailItemShared
    {
        public string Severity { get; set; }
        [JsonProperty("recipient-domain")]
        public string RecipientDomain { get; set; }
        [JsonProperty("id")]
        public string UniqueId { get; set; }
        public string Reason { get; set; }
        [JsonProperty("log-level")]
        public string LogLevel { get; set; }
        public double Timestamp { get; set; }
        public string Recipient { get; set; }
        public string Event { get; set; }
    }
}
