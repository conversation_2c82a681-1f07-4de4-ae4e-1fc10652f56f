using System;
using System.ComponentModel.DataAnnotations;

namespace Sacrra.Membership.Database.publicShared
{
    public class SRNDatesShared
    {
        #region Daily File Dates
        [Display(Name = "Daily File Development Start Date")]
        public DateTime? DailyFileDevelopmentStartDate { get; set; }

        [Display(Name = "Daily File Development End Date")]
        public DateTime? DailyFileDevelopmentEndDate { get; set; }

        [Display(Name = "Daily File Test Start Date")]
        public DateTime? DailyFileTestStartDate { get; set; }

        [Display(Name = "Daily File Test End Date")]
        public DateTime? DailyFileTestEndDate { get; set; }

        [Display(Name = "Daily File Go Live Date")]
        public DateTime? DailyFileGoLiveDate { get; set; }

        #endregion

        #region Monthly File Dates

        [Display(Name = "Monthly File Development Start Date")]
        public DateTime? MonthlyFileDevelopmentStartDate { get; set; }

        [Display(Name = "Monthly File Development End Date")]
        public DateTime? MonthlyFileDevelopmentEndDate { get; set; }

        [Display(Name = "Monthly File Test Start Date")]
        public DateTime? MonthlyFileTestStartDate { get; set; }

        [Display(Name = "Monthly File Test End Date")]
        public DateTime? MonthlyFileTestEndDate { get; set; }

        [Display(Name = "Monthly File Go Live Date")]
        public DateTime? MonthlyFileGoLiveDate { get; set; }

        #endregion
    }
}
