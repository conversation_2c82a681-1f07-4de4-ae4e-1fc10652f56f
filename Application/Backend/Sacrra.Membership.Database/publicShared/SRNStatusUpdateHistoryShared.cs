using System;
using Sacrra.Membership.Database.Enums;
using System.ComponentModel.DataAnnotations;

namespace Sacrra.Membership.Database.publicShared
{
    public abstract class SRNStatusUpdateHistoryShared : SRNDatesShared
    {
        [Display(Name = "Is Live File Submissions Suspended")]
        public bool IsLiveFileSubmissionsSuspended { get; set; }

        [Display(Name = "Comments")]
        public string Comments { get; set; }

        [Display(Name = "Status Type")]
        public SRNStatusTypes? UpdateType { get; set; }

        [Display(Name = "Bureau Instruction")]
        public string BureauInstruction { get; set; }

        [Display(Name = "File Type")]
        public SRNStatusFileTypes? FileType { get; set; }

        [Display(Name = "Last Submission Date")]
        //TODO: this needs to change to a DateTime type
        public DateTime? LastSubmissionDate { get; set; }
    }
}
