using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Sacrra.Membership.Freshdesk.DTOs
{
    public abstract class CompanyShared
    {
        public string Name { get; set; }
        public List<string> Domains { get; set; }
        public string Description { get; set; }
        [JsonPropertyName("health_score")]
        public string HealthScore { get; set; }
        [JsonPropertyName("account_tier")]
        public string AccountTier { get; set; }
        [JsonPropertyName("renewal_date")]
        public string RenewalDate { get; set; }
    }
}
