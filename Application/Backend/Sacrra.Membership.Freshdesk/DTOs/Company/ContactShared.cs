using Newtonsoft.Json;
using System.Collections.Generic;

namespace Sacrra.Membership.Freshdesk.DTOs
{
    public abstract class ContactShared
    {
        [JsonProperty("active")]
        public bool Active { get; set; }

        [JsonProperty("address")]
        public string Address { get; set; }

        [JsonProperty("company_id")]
        public long? CompanyId { get; set; }

        [JsonProperty("view_all_tickets")]
        public bool ViewAllTickets { get; set; }

        [JsonProperty("description")]
        public string Description { get; set; }

        [JsonProperty("email")]
        public string Email { get; set; }

        [JsonProperty("id")]
        public long Id { get; set; }

        [JsonProperty("job_title")]
        public string JobTitle { get; set; }

        [JsonProperty("language")]
        public string Language { get; set; }

        [JsonProperty("mobile")]
        public string Mobile { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("phone")]
        public string Phone { get; set; }

        [Json<PERSON>roperty("time_zone")]
        public string TimeZone { get; set; }

        [Json<PERSON>roperty("twitter_id")]
        public string TwitterId { get; set; }

        [Json<PERSON>roperty("other_emails")]
        public List<string> OtherEmails { get; set; }

        [JsonProperty("tags")]
        public List<string> Tags { get; set; }

        //[JsonProperty("avatar")]
        //public object Avatar { get; set; }
    }
}
