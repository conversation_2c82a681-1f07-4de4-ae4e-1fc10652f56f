using System;

namespace Sacrra.Membership.Freshdesk.DTOs
{
    public class TicketFilterInputDTO
    {
        public long Id { get; set; }
        public long? AgentId { get; set; }
        public long? StatusId { get; set; }
        public string Type { get; set; }
        public string RaisedBy { get; set; }
        public string Subject { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public DateTime? ResolutionDate { get; set; }
        public DateTime? CreationDateFrom { get; set; }
        public DateTime? CreationDateTo { get; set; }
    }
}
