using Sacrra.Membership.Freshdesk.DTOs.Ticket;

namespace Sacrra.Membership.Freshdesk.DTOs
{
    public class TicketGetDTO
    {
        public int Id { get; set; }
        public string Agent { get; set; }
        public string Type { get; set; }
        public string Subject { get; set; }
        public string Status { get; set; }
        public string RequestedBy { get; set; }
        public string ResolutionDate { get; set; }
        public string UpdatedAt { get; set; }
        public int TicketCoversationCount { get; set; }
        public string DueByDate { get; set; }
        public string ImpactedMember { get; set; }
    }
}
