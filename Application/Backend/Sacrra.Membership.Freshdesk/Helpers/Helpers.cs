using Microsoft.Extensions.Configuration;
using RestSharp;
using RestSharp.Authenticators;
using System;

namespace Sacrra.Membership.Freshdesk
{
    public static class Helpers
    {
        public static RestClient GetRestClient(IConfiguration configuration)
        {
            if(configuration != null)
            {
                var options = new RestClientOptions(configuration["FreshdeskIntegration:BaseURL"])
                {
                    Authenticator = new HttpBasicAuthenticator(configuration["FreshdeskIntegration:APIKey"], "api"),
                };
                var client = new RestClient(options);

                return client;
            }
            return null;
        }
    }
}
