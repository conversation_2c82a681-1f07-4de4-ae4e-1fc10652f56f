namespace Sacrra.Membership.Notification.Helpers
{
    public class EmailSettings
    {
        #region Email Settings
        public string AdminEmail { get; set; }
        public string SmtpUsername { get; set; }
        public string EmailFromName { get; set; }
        public string EmailFromAddress { get; set; }
        public string SmtpPassword { get; set; }
        public string SmtpServer { get; set; }
        public int SmtpPort { get; set; }
        public bool SmtpEnableSsl { get; set; }
        public string EmailTemplateFilePath { get; set; }
        public bool IsEmailEnabled { get; set; }
        public int RetryLimit { get; set; }
        public bool SendEmailOnEnv { get; set; }
        #endregion
    }
}
