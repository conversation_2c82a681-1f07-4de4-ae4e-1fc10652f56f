using System;

namespace Sacrra.Membership.Reporting.Models.DailyLoadReportModels
{
    public class DailySRNDetailsTableModel: DetailsGridItem
    {
        public DateTime TransactionDate { get; set; }
        public string MemberCompanyName { get; set; }
        public string MemberTradingName { get; set; }
        public string SRNNumber { get; set; }
        public string SPNumber { get; set; }
        public int TranYear { get; set; }
        public int TranMonth { get; set; }
        public int TranDay { get; set; }
        public long TotalRejected { get; set; }
        public long TotalReceived { get; set; }
        public long RegistrationsRejected { get; set; }
        public long RegistrationsReceived { get; set; }
        public long ClosuresReceived { get; set; }
        public long ClosuresRejected { get; set; }
        public double RegistrationRejectedPercentage { get; set; }
        public double ClosuresRejectedPercentage { get; set; }
        public double TotalRejectedPercentage { get; set; }
    }
}
