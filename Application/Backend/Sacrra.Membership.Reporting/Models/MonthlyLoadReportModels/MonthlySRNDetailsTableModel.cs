using System;

namespace Sacrra.Membership.Reporting.Models.MonthlyLoadReportModels
{
    public class MonthlySRNDetailsTableModel: DetailsGridItem
    {
        public DateTime TransactionDate { get; set; }
        public string MemberCompanyName { get; set; }
        public string MemberTradingName { get; set; }
        public string SRNNumber { get; set; }
        public string SPNumber { get; set; }
        public long TotalRejected { get; set; }
        public long DqRecordsRejected { get; set; }
        public long TotalReceived { get; set; }
        public double TotalRejectedPercentage { get; set; }
    }
}
