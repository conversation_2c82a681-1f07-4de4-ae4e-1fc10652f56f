using System;

namespace Sacrra.Membership.Reporting.Models.OutOfSLAReportModels
{
    public class OutOfSlaItem: DetailsGridItem
    {
        public long FctDTHExceptionsID { get; set; }
        public string MemberTradingName { get; set; }
        public int EndOfMonthDate { get; set; }
        public DateTime EndOfMonthDateAsDate { get; set; }
        public string YearMonth { get; set; }
        public string MemberCompanyName { get; set; }
        public string SPNumber { get; set; }
        public string SRNNumber { get; set; }
        public string IsSRNSubmitted { get; set; }
        public string IsSRNLoaded { get; set; }
        public string IsSRNOutOfSLA { get; set; }
        public string IsPending { get; set; }
        public double TotalDaysOutOfSLA { get; set; }
        public DateTime? SRNMaxSLAReceiveByDate { get; set; }
        public DateTime? DTHPushDate { get; set; }
        public DateTime? DTHPullDate { get; set; }
        public DateTime? DTHReceivedDate { get; set; }
        public DateTime? MonthlyFileLoadDate { get; set; }
        public DateTime? FileDate { get; set; }
        public int? ConsMonthsOSLA { get; set; }
        public int? MonthsPerYearOSLA { get; set; }
        public int? ConsMonthsNoSubmit { get; set; }
        public int? MonthsPerYearNoSubmit { get; set; }
        public string DimDTHReportType { get; set; }
        public string OSLAReason { get; set; }
        public string SHM { get; set; }
    }
}
